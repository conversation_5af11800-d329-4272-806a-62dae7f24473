{"buildFiles": ["C:\\src\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\APP\\urbaypro_v2\\android\\app\\.cxx\\Debug\\1xa4o2xv\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\APP\\urbaypro_v2\\android\\app\\.cxx\\Debug\\1xa4o2xv\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}