import 'package:flutter/material.dart';

/// Widget qui affiche le logo texte de Seqqo
class SeqqoLogo extends StatelessWidget {
  final double fontSize;
  final Color? color;
  final bool withBackground;

  const SeqqoLogo({
    super.key,
    this.fontSize = 36,
    this.color,
    this.withBackground = false,
  });

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? Theme.of(context).colorScheme.primary;

    final logoText = Text(
      'Seqqo',
      style: TextStyle(
        // Utiliser une liste de polices de secours
        fontFamily: 'RedditSans',
        fontFamilyFallback: const ['Roboto', 'Noto Sans'],
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: withBackground ? Colors.white : color,
        letterSpacing: -0.5,
      ),
    );

    if (withBackground) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: logoColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: logoText,
      );
    }

    return logoText;
  }
}
