// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'acceptance_report.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AcceptanceReport _$AcceptanceReportFromJson(Map<String, dynamic> json) {
  return _AcceptanceReport.fromJson(json);
}

/// @nodoc
mixin _$AcceptanceReport {
  /// Identifiant unique du PV
  String get id => throw _privateConstructorUsedError;

  /// Identifiant du projet
  String get projectId => throw _privateConstructorUsedError;

  /// Numéro du PV (auto-incrémenté)
  int get reportNumber => throw _privateConstructorUsedError;

  /// Titre du PV
  String get title => throw _privateConstructorUsedError;

  /// Lieu de la réception
  String get location => throw _privateConstructorUsedError;

  /// Date de la réception
  @TimestampConverter()
  DateTime get receptionDate => throw _privateConstructorUsedError;

  /// Date de création
  @TimestampConverter()
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Date de dernière modification
  @TimestampConverter()
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Description optionnelle
  String? get description => throw _privateConstructorUsedError;

  /// Liste des participants (noms)
  List<String> get participants => throw _privateConstructorUsedError;

  /// Liste des entreprises présentes (IDs)
  List<String> get presentCompanies => throw _privateConstructorUsedError;

  /// Liste des entreprises absentes (IDs)
  List<String> get absentCompanies => throw _privateConstructorUsedError;

  /// Liste des entreprises excusées (IDs)
  List<String> get excusedCompanies => throw _privateConstructorUsedError;

  /// Liste des réservations (défauts à corriger)
  List<Reservation> get reservations => throw _privateConstructorUsedError;

  /// Liste des positions des réservations sur le plan (stockée sous forme de JSON)
  String get reservationPositionsJson => throw _privateConstructorUsedError;

  /// Liste des signatures
  List<Signature> get signatures => throw _privateConstructorUsedError;

  /// Liste des URLs des documents partagés
  List<String> get sharedDocuments => throw _privateConstructorUsedError;

  /// Statut du PV (brouillon, envoyé, validé)
  String get status => throw _privateConstructorUsedError;

  /// Indique si le PV est supprimé (soft delete)
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Identifiant de l'utilisateur qui a créé le PV
  String? get createdBy => throw _privateConstructorUsedError;

  /// Date limite pour lever les réserves
  @TimestampConverter()
  DateTime? get deadlineDate => throw _privateConstructorUsedError;

  /// Notes additionnelles
  String? get additionalNotes => throw _privateConstructorUsedError;

  /// Identifiant de l'organisation
  String? get organizationId => throw _privateConstructorUsedError;

  /// URL du plan du site
  String? get sitePlanUrl => throw _privateConstructorUsedError;

  /// Serializes this AcceptanceReport to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AcceptanceReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AcceptanceReportCopyWith<AcceptanceReport> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AcceptanceReportCopyWith<$Res> {
  factory $AcceptanceReportCopyWith(
          AcceptanceReport value, $Res Function(AcceptanceReport) then) =
      _$AcceptanceReportCopyWithImpl<$Res, AcceptanceReport>;
  @useResult
  $Res call(
      {String id,
      String projectId,
      int reportNumber,
      String title,
      String location,
      @TimestampConverter() DateTime receptionDate,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime updatedAt,
      String? description,
      List<String> participants,
      List<String> presentCompanies,
      List<String> absentCompanies,
      List<String> excusedCompanies,
      List<Reservation> reservations,
      String reservationPositionsJson,
      List<Signature> signatures,
      List<String> sharedDocuments,
      String status,
      bool isDeleted,
      String? createdBy,
      @TimestampConverter() DateTime? deadlineDate,
      String? additionalNotes,
      String? organizationId,
      String? sitePlanUrl});
}

/// @nodoc
class _$AcceptanceReportCopyWithImpl<$Res, $Val extends AcceptanceReport>
    implements $AcceptanceReportCopyWith<$Res> {
  _$AcceptanceReportCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AcceptanceReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? projectId = null,
    Object? reportNumber = null,
    Object? title = null,
    Object? location = null,
    Object? receptionDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? description = freezed,
    Object? participants = null,
    Object? presentCompanies = null,
    Object? absentCompanies = null,
    Object? excusedCompanies = null,
    Object? reservations = null,
    Object? reservationPositionsJson = null,
    Object? signatures = null,
    Object? sharedDocuments = null,
    Object? status = null,
    Object? isDeleted = null,
    Object? createdBy = freezed,
    Object? deadlineDate = freezed,
    Object? additionalNotes = freezed,
    Object? organizationId = freezed,
    Object? sitePlanUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      reportNumber: null == reportNumber
          ? _value.reportNumber
          : reportNumber // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      receptionDate: null == receptionDate
          ? _value.receptionDate
          : receptionDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      participants: null == participants
          ? _value.participants
          : participants // ignore: cast_nullable_to_non_nullable
              as List<String>,
      presentCompanies: null == presentCompanies
          ? _value.presentCompanies
          : presentCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      absentCompanies: null == absentCompanies
          ? _value.absentCompanies
          : absentCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      excusedCompanies: null == excusedCompanies
          ? _value.excusedCompanies
          : excusedCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      reservations: null == reservations
          ? _value.reservations
          : reservations // ignore: cast_nullable_to_non_nullable
              as List<Reservation>,
      reservationPositionsJson: null == reservationPositionsJson
          ? _value.reservationPositionsJson
          : reservationPositionsJson // ignore: cast_nullable_to_non_nullable
              as String,
      signatures: null == signatures
          ? _value.signatures
          : signatures // ignore: cast_nullable_to_non_nullable
              as List<Signature>,
      sharedDocuments: null == sharedDocuments
          ? _value.sharedDocuments
          : sharedDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _value.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      sitePlanUrl: freezed == sitePlanUrl
          ? _value.sitePlanUrl
          : sitePlanUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AcceptanceReportImplCopyWith<$Res>
    implements $AcceptanceReportCopyWith<$Res> {
  factory _$$AcceptanceReportImplCopyWith(_$AcceptanceReportImpl value,
          $Res Function(_$AcceptanceReportImpl) then) =
      __$$AcceptanceReportImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String projectId,
      int reportNumber,
      String title,
      String location,
      @TimestampConverter() DateTime receptionDate,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime updatedAt,
      String? description,
      List<String> participants,
      List<String> presentCompanies,
      List<String> absentCompanies,
      List<String> excusedCompanies,
      List<Reservation> reservations,
      String reservationPositionsJson,
      List<Signature> signatures,
      List<String> sharedDocuments,
      String status,
      bool isDeleted,
      String? createdBy,
      @TimestampConverter() DateTime? deadlineDate,
      String? additionalNotes,
      String? organizationId,
      String? sitePlanUrl});
}

/// @nodoc
class __$$AcceptanceReportImplCopyWithImpl<$Res>
    extends _$AcceptanceReportCopyWithImpl<$Res, _$AcceptanceReportImpl>
    implements _$$AcceptanceReportImplCopyWith<$Res> {
  __$$AcceptanceReportImplCopyWithImpl(_$AcceptanceReportImpl _value,
      $Res Function(_$AcceptanceReportImpl) _then)
      : super(_value, _then);

  /// Create a copy of AcceptanceReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? projectId = null,
    Object? reportNumber = null,
    Object? title = null,
    Object? location = null,
    Object? receptionDate = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? description = freezed,
    Object? participants = null,
    Object? presentCompanies = null,
    Object? absentCompanies = null,
    Object? excusedCompanies = null,
    Object? reservations = null,
    Object? reservationPositionsJson = null,
    Object? signatures = null,
    Object? sharedDocuments = null,
    Object? status = null,
    Object? isDeleted = null,
    Object? createdBy = freezed,
    Object? deadlineDate = freezed,
    Object? additionalNotes = freezed,
    Object? organizationId = freezed,
    Object? sitePlanUrl = freezed,
  }) {
    return _then(_$AcceptanceReportImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      reportNumber: null == reportNumber
          ? _value.reportNumber
          : reportNumber // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      receptionDate: null == receptionDate
          ? _value.receptionDate
          : receptionDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      participants: null == participants
          ? _value._participants
          : participants // ignore: cast_nullable_to_non_nullable
              as List<String>,
      presentCompanies: null == presentCompanies
          ? _value._presentCompanies
          : presentCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      absentCompanies: null == absentCompanies
          ? _value._absentCompanies
          : absentCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      excusedCompanies: null == excusedCompanies
          ? _value._excusedCompanies
          : excusedCompanies // ignore: cast_nullable_to_non_nullable
              as List<String>,
      reservations: null == reservations
          ? _value._reservations
          : reservations // ignore: cast_nullable_to_non_nullable
              as List<Reservation>,
      reservationPositionsJson: null == reservationPositionsJson
          ? _value.reservationPositionsJson
          : reservationPositionsJson // ignore: cast_nullable_to_non_nullable
              as String,
      signatures: null == signatures
          ? _value._signatures
          : signatures // ignore: cast_nullable_to_non_nullable
              as List<Signature>,
      sharedDocuments: null == sharedDocuments
          ? _value._sharedDocuments
          : sharedDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _value.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      sitePlanUrl: freezed == sitePlanUrl
          ? _value.sitePlanUrl
          : sitePlanUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AcceptanceReportImpl implements _AcceptanceReport {
  const _$AcceptanceReportImpl(
      {required this.id,
      required this.projectId,
      required this.reportNumber,
      required this.title,
      required this.location,
      @TimestampConverter() required this.receptionDate,
      @TimestampConverter() required this.createdAt,
      @TimestampConverter() required this.updatedAt,
      this.description,
      final List<String> participants = const [],
      final List<String> presentCompanies = const [],
      final List<String> absentCompanies = const [],
      final List<String> excusedCompanies = const [],
      final List<Reservation> reservations = const [],
      this.reservationPositionsJson = '[]',
      final List<Signature> signatures = const [],
      final List<String> sharedDocuments = const [],
      this.status = 'draft',
      this.isDeleted = false,
      this.createdBy,
      @TimestampConverter() this.deadlineDate,
      this.additionalNotes,
      this.organizationId,
      this.sitePlanUrl})
      : _participants = participants,
        _presentCompanies = presentCompanies,
        _absentCompanies = absentCompanies,
        _excusedCompanies = excusedCompanies,
        _reservations = reservations,
        _signatures = signatures,
        _sharedDocuments = sharedDocuments;

  factory _$AcceptanceReportImpl.fromJson(Map<String, dynamic> json) =>
      _$$AcceptanceReportImplFromJson(json);

  /// Identifiant unique du PV
  @override
  final String id;

  /// Identifiant du projet
  @override
  final String projectId;

  /// Numéro du PV (auto-incrémenté)
  @override
  final int reportNumber;

  /// Titre du PV
  @override
  final String title;

  /// Lieu de la réception
  @override
  final String location;

  /// Date de la réception
  @override
  @TimestampConverter()
  final DateTime receptionDate;

  /// Date de création
  @override
  @TimestampConverter()
  final DateTime createdAt;

  /// Date de dernière modification
  @override
  @TimestampConverter()
  final DateTime updatedAt;

  /// Description optionnelle
  @override
  final String? description;

  /// Liste des participants (noms)
  final List<String> _participants;

  /// Liste des participants (noms)
  @override
  @JsonKey()
  List<String> get participants {
    if (_participants is EqualUnmodifiableListView) return _participants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_participants);
  }

  /// Liste des entreprises présentes (IDs)
  final List<String> _presentCompanies;

  /// Liste des entreprises présentes (IDs)
  @override
  @JsonKey()
  List<String> get presentCompanies {
    if (_presentCompanies is EqualUnmodifiableListView)
      return _presentCompanies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_presentCompanies);
  }

  /// Liste des entreprises absentes (IDs)
  final List<String> _absentCompanies;

  /// Liste des entreprises absentes (IDs)
  @override
  @JsonKey()
  List<String> get absentCompanies {
    if (_absentCompanies is EqualUnmodifiableListView) return _absentCompanies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_absentCompanies);
  }

  /// Liste des entreprises excusées (IDs)
  final List<String> _excusedCompanies;

  /// Liste des entreprises excusées (IDs)
  @override
  @JsonKey()
  List<String> get excusedCompanies {
    if (_excusedCompanies is EqualUnmodifiableListView)
      return _excusedCompanies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excusedCompanies);
  }

  /// Liste des réservations (défauts à corriger)
  final List<Reservation> _reservations;

  /// Liste des réservations (défauts à corriger)
  @override
  @JsonKey()
  List<Reservation> get reservations {
    if (_reservations is EqualUnmodifiableListView) return _reservations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reservations);
  }

  /// Liste des positions des réservations sur le plan (stockée sous forme de JSON)
  @override
  @JsonKey()
  final String reservationPositionsJson;

  /// Liste des signatures
  final List<Signature> _signatures;

  /// Liste des signatures
  @override
  @JsonKey()
  List<Signature> get signatures {
    if (_signatures is EqualUnmodifiableListView) return _signatures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_signatures);
  }

  /// Liste des URLs des documents partagés
  final List<String> _sharedDocuments;

  /// Liste des URLs des documents partagés
  @override
  @JsonKey()
  List<String> get sharedDocuments {
    if (_sharedDocuments is EqualUnmodifiableListView) return _sharedDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sharedDocuments);
  }

  /// Statut du PV (brouillon, envoyé, validé)
  @override
  @JsonKey()
  final String status;

  /// Indique si le PV est supprimé (soft delete)
  @override
  @JsonKey()
  final bool isDeleted;

  /// Identifiant de l'utilisateur qui a créé le PV
  @override
  final String? createdBy;

  /// Date limite pour lever les réserves
  @override
  @TimestampConverter()
  final DateTime? deadlineDate;

  /// Notes additionnelles
  @override
  final String? additionalNotes;

  /// Identifiant de l'organisation
  @override
  final String? organizationId;

  /// URL du plan du site
  @override
  final String? sitePlanUrl;

  @override
  String toString() {
    return 'AcceptanceReport(id: $id, projectId: $projectId, reportNumber: $reportNumber, title: $title, location: $location, receptionDate: $receptionDate, createdAt: $createdAt, updatedAt: $updatedAt, description: $description, participants: $participants, presentCompanies: $presentCompanies, absentCompanies: $absentCompanies, excusedCompanies: $excusedCompanies, reservations: $reservations, reservationPositionsJson: $reservationPositionsJson, signatures: $signatures, sharedDocuments: $sharedDocuments, status: $status, isDeleted: $isDeleted, createdBy: $createdBy, deadlineDate: $deadlineDate, additionalNotes: $additionalNotes, organizationId: $organizationId, sitePlanUrl: $sitePlanUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AcceptanceReportImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.reportNumber, reportNumber) ||
                other.reportNumber == reportNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.receptionDate, receptionDate) ||
                other.receptionDate == receptionDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._participants, _participants) &&
            const DeepCollectionEquality()
                .equals(other._presentCompanies, _presentCompanies) &&
            const DeepCollectionEquality()
                .equals(other._absentCompanies, _absentCompanies) &&
            const DeepCollectionEquality()
                .equals(other._excusedCompanies, _excusedCompanies) &&
            const DeepCollectionEquality()
                .equals(other._reservations, _reservations) &&
            (identical(
                    other.reservationPositionsJson, reservationPositionsJson) ||
                other.reservationPositionsJson == reservationPositionsJson) &&
            const DeepCollectionEquality()
                .equals(other._signatures, _signatures) &&
            const DeepCollectionEquality()
                .equals(other._sharedDocuments, _sharedDocuments) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.deadlineDate, deadlineDate) ||
                other.deadlineDate == deadlineDate) &&
            (identical(other.additionalNotes, additionalNotes) ||
                other.additionalNotes == additionalNotes) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.sitePlanUrl, sitePlanUrl) ||
                other.sitePlanUrl == sitePlanUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        projectId,
        reportNumber,
        title,
        location,
        receptionDate,
        createdAt,
        updatedAt,
        description,
        const DeepCollectionEquality().hash(_participants),
        const DeepCollectionEquality().hash(_presentCompanies),
        const DeepCollectionEquality().hash(_absentCompanies),
        const DeepCollectionEquality().hash(_excusedCompanies),
        const DeepCollectionEquality().hash(_reservations),
        reservationPositionsJson,
        const DeepCollectionEquality().hash(_signatures),
        const DeepCollectionEquality().hash(_sharedDocuments),
        status,
        isDeleted,
        createdBy,
        deadlineDate,
        additionalNotes,
        organizationId,
        sitePlanUrl
      ]);

  /// Create a copy of AcceptanceReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AcceptanceReportImplCopyWith<_$AcceptanceReportImpl> get copyWith =>
      __$$AcceptanceReportImplCopyWithImpl<_$AcceptanceReportImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AcceptanceReportImplToJson(
      this,
    );
  }
}

abstract class _AcceptanceReport implements AcceptanceReport {
  const factory _AcceptanceReport(
      {required final String id,
      required final String projectId,
      required final int reportNumber,
      required final String title,
      required final String location,
      @TimestampConverter() required final DateTime receptionDate,
      @TimestampConverter() required final DateTime createdAt,
      @TimestampConverter() required final DateTime updatedAt,
      final String? description,
      final List<String> participants,
      final List<String> presentCompanies,
      final List<String> absentCompanies,
      final List<String> excusedCompanies,
      final List<Reservation> reservations,
      final String reservationPositionsJson,
      final List<Signature> signatures,
      final List<String> sharedDocuments,
      final String status,
      final bool isDeleted,
      final String? createdBy,
      @TimestampConverter() final DateTime? deadlineDate,
      final String? additionalNotes,
      final String? organizationId,
      final String? sitePlanUrl}) = _$AcceptanceReportImpl;

  factory _AcceptanceReport.fromJson(Map<String, dynamic> json) =
      _$AcceptanceReportImpl.fromJson;

  /// Identifiant unique du PV
  @override
  String get id;

  /// Identifiant du projet
  @override
  String get projectId;

  /// Numéro du PV (auto-incrémenté)
  @override
  int get reportNumber;

  /// Titre du PV
  @override
  String get title;

  /// Lieu de la réception
  @override
  String get location;

  /// Date de la réception
  @override
  @TimestampConverter()
  DateTime get receptionDate;

  /// Date de création
  @override
  @TimestampConverter()
  DateTime get createdAt;

  /// Date de dernière modification
  @override
  @TimestampConverter()
  DateTime get updatedAt;

  /// Description optionnelle
  @override
  String? get description;

  /// Liste des participants (noms)
  @override
  List<String> get participants;

  /// Liste des entreprises présentes (IDs)
  @override
  List<String> get presentCompanies;

  /// Liste des entreprises absentes (IDs)
  @override
  List<String> get absentCompanies;

  /// Liste des entreprises excusées (IDs)
  @override
  List<String> get excusedCompanies;

  /// Liste des réservations (défauts à corriger)
  @override
  List<Reservation> get reservations;

  /// Liste des positions des réservations sur le plan (stockée sous forme de JSON)
  @override
  String get reservationPositionsJson;

  /// Liste des signatures
  @override
  List<Signature> get signatures;

  /// Liste des URLs des documents partagés
  @override
  List<String> get sharedDocuments;

  /// Statut du PV (brouillon, envoyé, validé)
  @override
  String get status;

  /// Indique si le PV est supprimé (soft delete)
  @override
  bool get isDeleted;

  /// Identifiant de l'utilisateur qui a créé le PV
  @override
  String? get createdBy;

  /// Date limite pour lever les réserves
  @override
  @TimestampConverter()
  DateTime? get deadlineDate;

  /// Notes additionnelles
  @override
  String? get additionalNotes;

  /// Identifiant de l'organisation
  @override
  String? get organizationId;

  /// URL du plan du site
  @override
  String? get sitePlanUrl;

  /// Create a copy of AcceptanceReport
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AcceptanceReportImplCopyWith<_$AcceptanceReportImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
