// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'acceptance_report.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AcceptanceReportImpl _$$AcceptanceReportImplFromJson(
        Map<String, dynamic> json) =>
    _$AcceptanceReportImpl(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      reportNumber: (json['reportNumber'] as num).toInt(),
      title: json['title'] as String,
      location: json['location'] as String,
      receptionDate: const TimestampConverter()
          .fromJson(json['receptionDate'] as Timestamp),
      createdAt:
          const TimestampConverter().fromJson(json['createdAt'] as Timestamp),
      updatedAt:
          const TimestampConverter().fromJson(json['updatedAt'] as Timestamp),
      description: json['description'] as String?,
      participants: (json['participants'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      presentCompanies: (json['presentCompanies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      absentCompanies: (json['absentCompanies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      excusedCompanies: (json['excusedCompanies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      reservations: (json['reservations'] as List<dynamic>?)
              ?.map((e) => Reservation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      reservationPositionsJson:
          json['reservationPositionsJson'] as String? ?? '[]',
      signatures: (json['signatures'] as List<dynamic>?)
              ?.map((e) => Signature.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      sharedDocuments: (json['sharedDocuments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      status: json['status'] as String? ?? 'draft',
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdBy: json['createdBy'] as String?,
      deadlineDate: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['deadlineDate'], const TimestampConverter().fromJson),
      additionalNotes: json['additionalNotes'] as String?,
      organizationId: json['organizationId'] as String?,
      sitePlanUrl: json['sitePlanUrl'] as String?,
    );

Map<String, dynamic> _$$AcceptanceReportImplToJson(
        _$AcceptanceReportImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'projectId': instance.projectId,
      'reportNumber': instance.reportNumber,
      'title': instance.title,
      'location': instance.location,
      'receptionDate':
          const TimestampConverter().toJson(instance.receptionDate),
      'createdAt': const TimestampConverter().toJson(instance.createdAt),
      'updatedAt': const TimestampConverter().toJson(instance.updatedAt),
      'description': instance.description,
      'participants': instance.participants,
      'presentCompanies': instance.presentCompanies,
      'absentCompanies': instance.absentCompanies,
      'excusedCompanies': instance.excusedCompanies,
      'reservations': instance.reservations,
      'reservationPositionsJson': instance.reservationPositionsJson,
      'signatures': instance.signatures,
      'sharedDocuments': instance.sharedDocuments,
      'status': instance.status,
      'isDeleted': instance.isDeleted,
      'createdBy': instance.createdBy,
      'deadlineDate': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.deadlineDate, const TimestampConverter().toJson),
      'additionalNotes': instance.additionalNotes,
      'organizationId': instance.organizationId,
      'sitePlanUrl': instance.sitePlanUrl,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
