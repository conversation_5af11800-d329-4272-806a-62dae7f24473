import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uuid/uuid.dart';

part 'document_share.freezed.dart';
part 'document_share.g.dart';

/// Convertisseur pour Timestamp <-> DateTime
class TimestampConverter implements JsonConverter<DateTime, Timestamp> {
  const TimestampConverter();

  @override
  DateTime fromJson(Timestamp timestamp) => timestamp.toDate();

  @override
  Timestamp toJson(DateTime date) => Timestamp.fromDate(date);
}

/// Statut d'un partage de document
enum ShareStatus {
  /// Document en attente d'envoi
  pending,

  /// Document envoyé
  sent,

  /// Document ouvert par le destinataire
  opened,

  /// Document signé par le destinataire
  signed,

  /// Erreur lors de l'envoi
  error,
}

/// Type de document partagé
enum DocumentType {
  /// PV de réception
  acceptanceReport,

  /// Quitus de levée de réserves
  clearanceReceipt,

  /// Plan avec réserves
  sitePlan,

  /// Autre document
  other,
}

/// Modèle pour les partages de documents
@freezed
class DocumentShare with _$DocumentShare {
  /// Constructeur principal
  const factory DocumentShare({
    /// Identifiant unique du partage
    required String id,

    /// Identifiant du rapport associé
    required String reportId,

    /// Identifiant du projet associé
    required String projectId,

    /// Type de document partagé
    @Default(DocumentType.acceptanceReport) DocumentType documentType,

    /// Titre du document
    required String documentTitle,

    /// URL du document
    String? documentUrl,

    /// Nom du destinataire
    required String recipientName,

    /// Email du destinataire
    required String recipientEmail,

    /// Identifiant de l'entreprise destinataire (si applicable)
    String? recipientCompanyId,

    /// Nom de l'entreprise destinataire (si applicable)
    String? recipientCompanyName,

    /// Message personnalisé
    String? message,

    /// Statut du partage
    @Default(ShareStatus.pending) ShareStatus status,

    /// Date de création du partage
    @TimestampConverter() required DateTime createdAt,

    /// Date d'envoi du document
    @TimestampConverter() DateTime? sentAt,

    /// Date d'ouverture du document
    @TimestampConverter() DateTime? openedAt,

    /// Date de signature du document
    @TimestampConverter() DateTime? signedAt,

    /// Identifiant de l'utilisateur qui a créé le partage
    String? createdBy,

    /// Identifiant de l'organisation
    String? organizationId,

    /// Lien de signature unique
    String? signatureLink,

    /// Indique si le partage est supprimé (soft delete)
    @Default(false) bool isDeleted,
  }) = _DocumentShare;

  /// Crée un partage vide avec des valeurs par défaut
  factory DocumentShare.create({
    required String reportId,
    required String projectId,
    required String recipientName,
    required String recipientEmail,
    required DocumentType documentType,
    required String documentTitle,
    String? recipientCompanyId,
    String? recipientCompanyName,
    String? message,
    String? organizationId,
    String? createdBy,
  }) {
    final now = DateTime.now();
    final uuid = const Uuid().v4();

    return DocumentShare(
      id: uuid,
      reportId: reportId,
      projectId: projectId,
      documentType: documentType,
      documentTitle: documentTitle,
      recipientName: recipientName,
      recipientEmail: recipientEmail,
      recipientCompanyId: recipientCompanyId,
      recipientCompanyName: recipientCompanyName,
      message: message,
      createdAt: now,
      createdBy: createdBy,
      organizationId: organizationId,
      signatureLink: '${uuid.substring(0, 8)}-${uuid.substring(24)}',
    );
  }

  /// Conversion depuis JSON
  factory DocumentShare.fromJson(Map<String, dynamic> json) =>
      _$DocumentShareFromJson(json);
}
