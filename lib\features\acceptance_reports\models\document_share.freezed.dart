// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_share.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DocumentShare {
  /// Identifiant unique du partage
  String get id;

  /// Identifiant du rapport associé
  String get reportId;

  /// Identifiant du projet associé
  String get projectId;

  /// Type de document partagé
  DocumentType get documentType;

  /// Titre du document
  String get documentTitle;

  /// URL du document
  String? get documentUrl;

  /// Nom du destinataire
  String get recipientName;

  /// Email du destinataire
  String get recipientEmail;

  /// Identifiant de l'entreprise destinataire (si applicable)
  String? get recipientCompanyId;

  /// Nom de l'entreprise destinataire (si applicable)
  String? get recipientCompanyName;

  /// Message personnalisé
  String? get message;

  /// Statut du partage
  ShareStatus get status;

  /// Date de création du partage
  @TimestampConverter()
  DateTime get createdAt;

  /// Date d'envoi du document
  @TimestampConverter()
  DateTime? get sentAt;

  /// Date d'ouverture du document
  @TimestampConverter()
  DateTime? get openedAt;

  /// Date de signature du document
  @TimestampConverter()
  DateTime? get signedAt;

  /// Identifiant de l'utilisateur qui a créé le partage
  String? get createdBy;

  /// Identifiant de l'organisation
  String? get organizationId;

  /// Lien de signature unique
  String? get signatureLink;

  /// Indique si le partage est supprimé (soft delete)
  bool get isDeleted;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DocumentShareCopyWith<DocumentShare> get copyWith =>
      _$DocumentShareCopyWithImpl<DocumentShare>(
          this as DocumentShare, _$identity);

  /// Serializes this DocumentShare to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DocumentShare &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.reportId, reportId) ||
                other.reportId == reportId) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.documentTitle, documentTitle) ||
                other.documentTitle == documentTitle) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.recipientName, recipientName) ||
                other.recipientName == recipientName) &&
            (identical(other.recipientEmail, recipientEmail) ||
                other.recipientEmail == recipientEmail) &&
            (identical(other.recipientCompanyId, recipientCompanyId) ||
                other.recipientCompanyId == recipientCompanyId) &&
            (identical(other.recipientCompanyName, recipientCompanyName) ||
                other.recipientCompanyName == recipientCompanyName) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.sentAt, sentAt) || other.sentAt == sentAt) &&
            (identical(other.openedAt, openedAt) ||
                other.openedAt == openedAt) &&
            (identical(other.signedAt, signedAt) ||
                other.signedAt == signedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.signatureLink, signatureLink) ||
                other.signatureLink == signatureLink) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        reportId,
        projectId,
        documentType,
        documentTitle,
        documentUrl,
        recipientName,
        recipientEmail,
        recipientCompanyId,
        recipientCompanyName,
        message,
        status,
        createdAt,
        sentAt,
        openedAt,
        signedAt,
        createdBy,
        organizationId,
        signatureLink,
        isDeleted
      ]);

  @override
  String toString() {
    return 'DocumentShare(id: $id, reportId: $reportId, projectId: $projectId, documentType: $documentType, documentTitle: $documentTitle, documentUrl: $documentUrl, recipientName: $recipientName, recipientEmail: $recipientEmail, recipientCompanyId: $recipientCompanyId, recipientCompanyName: $recipientCompanyName, message: $message, status: $status, createdAt: $createdAt, sentAt: $sentAt, openedAt: $openedAt, signedAt: $signedAt, createdBy: $createdBy, organizationId: $organizationId, signatureLink: $signatureLink, isDeleted: $isDeleted)';
  }
}

/// @nodoc
abstract mixin class $DocumentShareCopyWith<$Res> {
  factory $DocumentShareCopyWith(
          DocumentShare value, $Res Function(DocumentShare) _then) =
      _$DocumentShareCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String reportId,
      String projectId,
      DocumentType documentType,
      String documentTitle,
      String? documentUrl,
      String recipientName,
      String recipientEmail,
      String? recipientCompanyId,
      String? recipientCompanyName,
      String? message,
      ShareStatus status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime? sentAt,
      @TimestampConverter() DateTime? openedAt,
      @TimestampConverter() DateTime? signedAt,
      String? createdBy,
      String? organizationId,
      String? signatureLink,
      bool isDeleted});
}

/// @nodoc
class _$DocumentShareCopyWithImpl<$Res>
    implements $DocumentShareCopyWith<$Res> {
  _$DocumentShareCopyWithImpl(this._self, this._then);

  final DocumentShare _self;
  final $Res Function(DocumentShare) _then;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? reportId = null,
    Object? projectId = null,
    Object? documentType = null,
    Object? documentTitle = null,
    Object? documentUrl = freezed,
    Object? recipientName = null,
    Object? recipientEmail = null,
    Object? recipientCompanyId = freezed,
    Object? recipientCompanyName = freezed,
    Object? message = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? sentAt = freezed,
    Object? openedAt = freezed,
    Object? signedAt = freezed,
    Object? createdBy = freezed,
    Object? organizationId = freezed,
    Object? signatureLink = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _self.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _self.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      documentType: null == documentType
          ? _self.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      documentTitle: null == documentTitle
          ? _self.documentTitle
          : documentTitle // ignore: cast_nullable_to_non_nullable
              as String,
      documentUrl: freezed == documentUrl
          ? _self.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientName: null == recipientName
          ? _self.recipientName
          : recipientName // ignore: cast_nullable_to_non_nullable
              as String,
      recipientEmail: null == recipientEmail
          ? _self.recipientEmail
          : recipientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      recipientCompanyId: freezed == recipientCompanyId
          ? _self.recipientCompanyId
          : recipientCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientCompanyName: freezed == recipientCompanyName
          ? _self.recipientCompanyName
          : recipientCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ShareStatus,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sentAt: freezed == sentAt
          ? _self.sentAt
          : sentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      openedAt: freezed == openedAt
          ? _self.openedAt
          : openedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      signedAt: freezed == signedAt
          ? _self.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _self.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureLink: freezed == signatureLink
          ? _self.signatureLink
          : signatureLink // ignore: cast_nullable_to_non_nullable
              as String?,
      isDeleted: null == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _DocumentShare implements DocumentShare {
  const _DocumentShare(
      {required this.id,
      required this.reportId,
      required this.projectId,
      this.documentType = DocumentType.acceptanceReport,
      required this.documentTitle,
      this.documentUrl,
      required this.recipientName,
      required this.recipientEmail,
      this.recipientCompanyId,
      this.recipientCompanyName,
      this.message,
      this.status = ShareStatus.pending,
      @TimestampConverter() required this.createdAt,
      @TimestampConverter() this.sentAt,
      @TimestampConverter() this.openedAt,
      @TimestampConverter() this.signedAt,
      this.createdBy,
      this.organizationId,
      this.signatureLink,
      this.isDeleted = false});
  factory _DocumentShare.fromJson(Map<String, dynamic> json) =>
      _$DocumentShareFromJson(json);

  /// Identifiant unique du partage
  @override
  final String id;

  /// Identifiant du rapport associé
  @override
  final String reportId;

  /// Identifiant du projet associé
  @override
  final String projectId;

  /// Type de document partagé
  @override
  @JsonKey()
  final DocumentType documentType;

  /// Titre du document
  @override
  final String documentTitle;

  /// URL du document
  @override
  final String? documentUrl;

  /// Nom du destinataire
  @override
  final String recipientName;

  /// Email du destinataire
  @override
  final String recipientEmail;

  /// Identifiant de l'entreprise destinataire (si applicable)
  @override
  final String? recipientCompanyId;

  /// Nom de l'entreprise destinataire (si applicable)
  @override
  final String? recipientCompanyName;

  /// Message personnalisé
  @override
  final String? message;

  /// Statut du partage
  @override
  @JsonKey()
  final ShareStatus status;

  /// Date de création du partage
  @override
  @TimestampConverter()
  final DateTime createdAt;

  /// Date d'envoi du document
  @override
  @TimestampConverter()
  final DateTime? sentAt;

  /// Date d'ouverture du document
  @override
  @TimestampConverter()
  final DateTime? openedAt;

  /// Date de signature du document
  @override
  @TimestampConverter()
  final DateTime? signedAt;

  /// Identifiant de l'utilisateur qui a créé le partage
  @override
  final String? createdBy;

  /// Identifiant de l'organisation
  @override
  final String? organizationId;

  /// Lien de signature unique
  @override
  final String? signatureLink;

  /// Indique si le partage est supprimé (soft delete)
  @override
  @JsonKey()
  final bool isDeleted;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DocumentShareCopyWith<_DocumentShare> get copyWith =>
      __$DocumentShareCopyWithImpl<_DocumentShare>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DocumentShareToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DocumentShare &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.reportId, reportId) ||
                other.reportId == reportId) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.documentTitle, documentTitle) ||
                other.documentTitle == documentTitle) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.recipientName, recipientName) ||
                other.recipientName == recipientName) &&
            (identical(other.recipientEmail, recipientEmail) ||
                other.recipientEmail == recipientEmail) &&
            (identical(other.recipientCompanyId, recipientCompanyId) ||
                other.recipientCompanyId == recipientCompanyId) &&
            (identical(other.recipientCompanyName, recipientCompanyName) ||
                other.recipientCompanyName == recipientCompanyName) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.sentAt, sentAt) || other.sentAt == sentAt) &&
            (identical(other.openedAt, openedAt) ||
                other.openedAt == openedAt) &&
            (identical(other.signedAt, signedAt) ||
                other.signedAt == signedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.signatureLink, signatureLink) ||
                other.signatureLink == signatureLink) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        reportId,
        projectId,
        documentType,
        documentTitle,
        documentUrl,
        recipientName,
        recipientEmail,
        recipientCompanyId,
        recipientCompanyName,
        message,
        status,
        createdAt,
        sentAt,
        openedAt,
        signedAt,
        createdBy,
        organizationId,
        signatureLink,
        isDeleted
      ]);

  @override
  String toString() {
    return 'DocumentShare(id: $id, reportId: $reportId, projectId: $projectId, documentType: $documentType, documentTitle: $documentTitle, documentUrl: $documentUrl, recipientName: $recipientName, recipientEmail: $recipientEmail, recipientCompanyId: $recipientCompanyId, recipientCompanyName: $recipientCompanyName, message: $message, status: $status, createdAt: $createdAt, sentAt: $sentAt, openedAt: $openedAt, signedAt: $signedAt, createdBy: $createdBy, organizationId: $organizationId, signatureLink: $signatureLink, isDeleted: $isDeleted)';
  }
}

/// @nodoc
abstract mixin class _$DocumentShareCopyWith<$Res>
    implements $DocumentShareCopyWith<$Res> {
  factory _$DocumentShareCopyWith(
          _DocumentShare value, $Res Function(_DocumentShare) _then) =
      __$DocumentShareCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String reportId,
      String projectId,
      DocumentType documentType,
      String documentTitle,
      String? documentUrl,
      String recipientName,
      String recipientEmail,
      String? recipientCompanyId,
      String? recipientCompanyName,
      String? message,
      ShareStatus status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime? sentAt,
      @TimestampConverter() DateTime? openedAt,
      @TimestampConverter() DateTime? signedAt,
      String? createdBy,
      String? organizationId,
      String? signatureLink,
      bool isDeleted});
}

/// @nodoc
class __$DocumentShareCopyWithImpl<$Res>
    implements _$DocumentShareCopyWith<$Res> {
  __$DocumentShareCopyWithImpl(this._self, this._then);

  final _DocumentShare _self;
  final $Res Function(_DocumentShare) _then;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? reportId = null,
    Object? projectId = null,
    Object? documentType = null,
    Object? documentTitle = null,
    Object? documentUrl = freezed,
    Object? recipientName = null,
    Object? recipientEmail = null,
    Object? recipientCompanyId = freezed,
    Object? recipientCompanyName = freezed,
    Object? message = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? sentAt = freezed,
    Object? openedAt = freezed,
    Object? signedAt = freezed,
    Object? createdBy = freezed,
    Object? organizationId = freezed,
    Object? signatureLink = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_DocumentShare(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _self.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _self.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      documentType: null == documentType
          ? _self.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      documentTitle: null == documentTitle
          ? _self.documentTitle
          : documentTitle // ignore: cast_nullable_to_non_nullable
              as String,
      documentUrl: freezed == documentUrl
          ? _self.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientName: null == recipientName
          ? _self.recipientName
          : recipientName // ignore: cast_nullable_to_non_nullable
              as String,
      recipientEmail: null == recipientEmail
          ? _self.recipientEmail
          : recipientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      recipientCompanyId: freezed == recipientCompanyId
          ? _self.recipientCompanyId
          : recipientCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientCompanyName: freezed == recipientCompanyName
          ? _self.recipientCompanyName
          : recipientCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ShareStatus,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sentAt: freezed == sentAt
          ? _self.sentAt
          : sentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      openedAt: freezed == openedAt
          ? _self.openedAt
          : openedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      signedAt: freezed == signedAt
          ? _self.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _self.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureLink: freezed == signatureLink
          ? _self.signatureLink
          : signatureLink // ignore: cast_nullable_to_non_nullable
              as String?,
      isDeleted: null == isDeleted
          ? _self.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
