// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_share.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DocumentShare _$DocumentShareFromJson(Map<String, dynamic> json) {
  return _DocumentShare.fromJson(json);
}

/// @nodoc
mixin _$DocumentShare {
  /// Identifiant unique du partage
  String get id => throw _privateConstructorUsedError;

  /// Identifiant du rapport associé
  String get reportId => throw _privateConstructorUsedError;

  /// Identifiant du projet associé
  String get projectId => throw _privateConstructorUsedError;

  /// Type de document partagé
  DocumentType get documentType => throw _privateConstructorUsedError;

  /// Titre du document
  String get documentTitle => throw _privateConstructorUsedError;

  /// URL du document
  String? get documentUrl => throw _privateConstructorUsedError;

  /// Nom du destinataire
  String get recipientName => throw _privateConstructorUsedError;

  /// Email du destinataire
  String get recipientEmail => throw _privateConstructorUsedError;

  /// Identifiant de l'entreprise destinataire (si applicable)
  String? get recipientCompanyId => throw _privateConstructorUsedError;

  /// Nom de l'entreprise destinataire (si applicable)
  String? get recipientCompanyName => throw _privateConstructorUsedError;

  /// Message personnalisé
  String? get message => throw _privateConstructorUsedError;

  /// Statut du partage
  ShareStatus get status => throw _privateConstructorUsedError;

  /// Date de création du partage
  @TimestampConverter()
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Date d'envoi du document
  @TimestampConverter()
  DateTime? get sentAt => throw _privateConstructorUsedError;

  /// Date d'ouverture du document
  @TimestampConverter()
  DateTime? get openedAt => throw _privateConstructorUsedError;

  /// Date de signature du document
  @TimestampConverter()
  DateTime? get signedAt => throw _privateConstructorUsedError;

  /// Identifiant de l'utilisateur qui a créé le partage
  String? get createdBy => throw _privateConstructorUsedError;

  /// Identifiant de l'organisation
  String? get organizationId => throw _privateConstructorUsedError;

  /// Lien de signature unique
  String? get signatureLink => throw _privateConstructorUsedError;

  /// Indique si le partage est supprimé (soft delete)
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this DocumentShare to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DocumentShareCopyWith<DocumentShare> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentShareCopyWith<$Res> {
  factory $DocumentShareCopyWith(
          DocumentShare value, $Res Function(DocumentShare) then) =
      _$DocumentShareCopyWithImpl<$Res, DocumentShare>;
  @useResult
  $Res call(
      {String id,
      String reportId,
      String projectId,
      DocumentType documentType,
      String documentTitle,
      String? documentUrl,
      String recipientName,
      String recipientEmail,
      String? recipientCompanyId,
      String? recipientCompanyName,
      String? message,
      ShareStatus status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime? sentAt,
      @TimestampConverter() DateTime? openedAt,
      @TimestampConverter() DateTime? signedAt,
      String? createdBy,
      String? organizationId,
      String? signatureLink,
      bool isDeleted});
}

/// @nodoc
class _$DocumentShareCopyWithImpl<$Res, $Val extends DocumentShare>
    implements $DocumentShareCopyWith<$Res> {
  _$DocumentShareCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? reportId = null,
    Object? projectId = null,
    Object? documentType = null,
    Object? documentTitle = null,
    Object? documentUrl = freezed,
    Object? recipientName = null,
    Object? recipientEmail = null,
    Object? recipientCompanyId = freezed,
    Object? recipientCompanyName = freezed,
    Object? message = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? sentAt = freezed,
    Object? openedAt = freezed,
    Object? signedAt = freezed,
    Object? createdBy = freezed,
    Object? organizationId = freezed,
    Object? signatureLink = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _value.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      documentType: null == documentType
          ? _value.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      documentTitle: null == documentTitle
          ? _value.documentTitle
          : documentTitle // ignore: cast_nullable_to_non_nullable
              as String,
      documentUrl: freezed == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientName: null == recipientName
          ? _value.recipientName
          : recipientName // ignore: cast_nullable_to_non_nullable
              as String,
      recipientEmail: null == recipientEmail
          ? _value.recipientEmail
          : recipientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      recipientCompanyId: freezed == recipientCompanyId
          ? _value.recipientCompanyId
          : recipientCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientCompanyName: freezed == recipientCompanyName
          ? _value.recipientCompanyName
          : recipientCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ShareStatus,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sentAt: freezed == sentAt
          ? _value.sentAt
          : sentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      openedAt: freezed == openedAt
          ? _value.openedAt
          : openedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      signedAt: freezed == signedAt
          ? _value.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _value.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureLink: freezed == signatureLink
          ? _value.signatureLink
          : signatureLink // ignore: cast_nullable_to_non_nullable
              as String?,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentShareImplCopyWith<$Res>
    implements $DocumentShareCopyWith<$Res> {
  factory _$$DocumentShareImplCopyWith(
          _$DocumentShareImpl value, $Res Function(_$DocumentShareImpl) then) =
      __$$DocumentShareImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String reportId,
      String projectId,
      DocumentType documentType,
      String documentTitle,
      String? documentUrl,
      String recipientName,
      String recipientEmail,
      String? recipientCompanyId,
      String? recipientCompanyName,
      String? message,
      ShareStatus status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime? sentAt,
      @TimestampConverter() DateTime? openedAt,
      @TimestampConverter() DateTime? signedAt,
      String? createdBy,
      String? organizationId,
      String? signatureLink,
      bool isDeleted});
}

/// @nodoc
class __$$DocumentShareImplCopyWithImpl<$Res>
    extends _$DocumentShareCopyWithImpl<$Res, _$DocumentShareImpl>
    implements _$$DocumentShareImplCopyWith<$Res> {
  __$$DocumentShareImplCopyWithImpl(
      _$DocumentShareImpl _value, $Res Function(_$DocumentShareImpl) _then)
      : super(_value, _then);

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? reportId = null,
    Object? projectId = null,
    Object? documentType = null,
    Object? documentTitle = null,
    Object? documentUrl = freezed,
    Object? recipientName = null,
    Object? recipientEmail = null,
    Object? recipientCompanyId = freezed,
    Object? recipientCompanyName = freezed,
    Object? message = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? sentAt = freezed,
    Object? openedAt = freezed,
    Object? signedAt = freezed,
    Object? createdBy = freezed,
    Object? organizationId = freezed,
    Object? signatureLink = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_$DocumentShareImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _value.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as String,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      documentType: null == documentType
          ? _value.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      documentTitle: null == documentTitle
          ? _value.documentTitle
          : documentTitle // ignore: cast_nullable_to_non_nullable
              as String,
      documentUrl: freezed == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientName: null == recipientName
          ? _value.recipientName
          : recipientName // ignore: cast_nullable_to_non_nullable
              as String,
      recipientEmail: null == recipientEmail
          ? _value.recipientEmail
          : recipientEmail // ignore: cast_nullable_to_non_nullable
              as String,
      recipientCompanyId: freezed == recipientCompanyId
          ? _value.recipientCompanyId
          : recipientCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientCompanyName: freezed == recipientCompanyName
          ? _value.recipientCompanyName
          : recipientCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ShareStatus,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      sentAt: freezed == sentAt
          ? _value.sentAt
          : sentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      openedAt: freezed == openedAt
          ? _value.openedAt
          : openedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      signedAt: freezed == signedAt
          ? _value.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      organizationId: freezed == organizationId
          ? _value.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureLink: freezed == signatureLink
          ? _value.signatureLink
          : signatureLink // ignore: cast_nullable_to_non_nullable
              as String?,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DocumentShareImpl implements _DocumentShare {
  const _$DocumentShareImpl(
      {required this.id,
      required this.reportId,
      required this.projectId,
      this.documentType = DocumentType.acceptanceReport,
      required this.documentTitle,
      this.documentUrl,
      required this.recipientName,
      required this.recipientEmail,
      this.recipientCompanyId,
      this.recipientCompanyName,
      this.message,
      this.status = ShareStatus.pending,
      @TimestampConverter() required this.createdAt,
      @TimestampConverter() this.sentAt,
      @TimestampConverter() this.openedAt,
      @TimestampConverter() this.signedAt,
      this.createdBy,
      this.organizationId,
      this.signatureLink,
      this.isDeleted = false});

  factory _$DocumentShareImpl.fromJson(Map<String, dynamic> json) =>
      _$$DocumentShareImplFromJson(json);

  /// Identifiant unique du partage
  @override
  final String id;

  /// Identifiant du rapport associé
  @override
  final String reportId;

  /// Identifiant du projet associé
  @override
  final String projectId;

  /// Type de document partagé
  @override
  @JsonKey()
  final DocumentType documentType;

  /// Titre du document
  @override
  final String documentTitle;

  /// URL du document
  @override
  final String? documentUrl;

  /// Nom du destinataire
  @override
  final String recipientName;

  /// Email du destinataire
  @override
  final String recipientEmail;

  /// Identifiant de l'entreprise destinataire (si applicable)
  @override
  final String? recipientCompanyId;

  /// Nom de l'entreprise destinataire (si applicable)
  @override
  final String? recipientCompanyName;

  /// Message personnalisé
  @override
  final String? message;

  /// Statut du partage
  @override
  @JsonKey()
  final ShareStatus status;

  /// Date de création du partage
  @override
  @TimestampConverter()
  final DateTime createdAt;

  /// Date d'envoi du document
  @override
  @TimestampConverter()
  final DateTime? sentAt;

  /// Date d'ouverture du document
  @override
  @TimestampConverter()
  final DateTime? openedAt;

  /// Date de signature du document
  @override
  @TimestampConverter()
  final DateTime? signedAt;

  /// Identifiant de l'utilisateur qui a créé le partage
  @override
  final String? createdBy;

  /// Identifiant de l'organisation
  @override
  final String? organizationId;

  /// Lien de signature unique
  @override
  final String? signatureLink;

  /// Indique si le partage est supprimé (soft delete)
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'DocumentShare(id: $id, reportId: $reportId, projectId: $projectId, documentType: $documentType, documentTitle: $documentTitle, documentUrl: $documentUrl, recipientName: $recipientName, recipientEmail: $recipientEmail, recipientCompanyId: $recipientCompanyId, recipientCompanyName: $recipientCompanyName, message: $message, status: $status, createdAt: $createdAt, sentAt: $sentAt, openedAt: $openedAt, signedAt: $signedAt, createdBy: $createdBy, organizationId: $organizationId, signatureLink: $signatureLink, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentShareImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.reportId, reportId) ||
                other.reportId == reportId) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.documentTitle, documentTitle) ||
                other.documentTitle == documentTitle) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.recipientName, recipientName) ||
                other.recipientName == recipientName) &&
            (identical(other.recipientEmail, recipientEmail) ||
                other.recipientEmail == recipientEmail) &&
            (identical(other.recipientCompanyId, recipientCompanyId) ||
                other.recipientCompanyId == recipientCompanyId) &&
            (identical(other.recipientCompanyName, recipientCompanyName) ||
                other.recipientCompanyName == recipientCompanyName) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.sentAt, sentAt) || other.sentAt == sentAt) &&
            (identical(other.openedAt, openedAt) ||
                other.openedAt == openedAt) &&
            (identical(other.signedAt, signedAt) ||
                other.signedAt == signedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.signatureLink, signatureLink) ||
                other.signatureLink == signatureLink) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        reportId,
        projectId,
        documentType,
        documentTitle,
        documentUrl,
        recipientName,
        recipientEmail,
        recipientCompanyId,
        recipientCompanyName,
        message,
        status,
        createdAt,
        sentAt,
        openedAt,
        signedAt,
        createdBy,
        organizationId,
        signatureLink,
        isDeleted
      ]);

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentShareImplCopyWith<_$DocumentShareImpl> get copyWith =>
      __$$DocumentShareImplCopyWithImpl<_$DocumentShareImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DocumentShareImplToJson(
      this,
    );
  }
}

abstract class _DocumentShare implements DocumentShare {
  const factory _DocumentShare(
      {required final String id,
      required final String reportId,
      required final String projectId,
      final DocumentType documentType,
      required final String documentTitle,
      final String? documentUrl,
      required final String recipientName,
      required final String recipientEmail,
      final String? recipientCompanyId,
      final String? recipientCompanyName,
      final String? message,
      final ShareStatus status,
      @TimestampConverter() required final DateTime createdAt,
      @TimestampConverter() final DateTime? sentAt,
      @TimestampConverter() final DateTime? openedAt,
      @TimestampConverter() final DateTime? signedAt,
      final String? createdBy,
      final String? organizationId,
      final String? signatureLink,
      final bool isDeleted}) = _$DocumentShareImpl;

  factory _DocumentShare.fromJson(Map<String, dynamic> json) =
      _$DocumentShareImpl.fromJson;

  /// Identifiant unique du partage
  @override
  String get id;

  /// Identifiant du rapport associé
  @override
  String get reportId;

  /// Identifiant du projet associé
  @override
  String get projectId;

  /// Type de document partagé
  @override
  DocumentType get documentType;

  /// Titre du document
  @override
  String get documentTitle;

  /// URL du document
  @override
  String? get documentUrl;

  /// Nom du destinataire
  @override
  String get recipientName;

  /// Email du destinataire
  @override
  String get recipientEmail;

  /// Identifiant de l'entreprise destinataire (si applicable)
  @override
  String? get recipientCompanyId;

  /// Nom de l'entreprise destinataire (si applicable)
  @override
  String? get recipientCompanyName;

  /// Message personnalisé
  @override
  String? get message;

  /// Statut du partage
  @override
  ShareStatus get status;

  /// Date de création du partage
  @override
  @TimestampConverter()
  DateTime get createdAt;

  /// Date d'envoi du document
  @override
  @TimestampConverter()
  DateTime? get sentAt;

  /// Date d'ouverture du document
  @override
  @TimestampConverter()
  DateTime? get openedAt;

  /// Date de signature du document
  @override
  @TimestampConverter()
  DateTime? get signedAt;

  /// Identifiant de l'utilisateur qui a créé le partage
  @override
  String? get createdBy;

  /// Identifiant de l'organisation
  @override
  String? get organizationId;

  /// Lien de signature unique
  @override
  String? get signatureLink;

  /// Indique si le partage est supprimé (soft delete)
  @override
  bool get isDeleted;

  /// Create a copy of DocumentShare
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentShareImplCopyWith<_$DocumentShareImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
