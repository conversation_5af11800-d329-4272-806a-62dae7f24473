// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_share.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DocumentShareImpl _$$DocumentShareImplFromJson(Map<String, dynamic> json) =>
    _$DocumentShareImpl(
      id: json['id'] as String,
      reportId: json['reportId'] as String,
      projectId: json['projectId'] as String,
      documentType:
          $enumDecodeNullable(_$DocumentTypeEnumMap, json['documentType']) ??
              DocumentType.acceptanceReport,
      documentTitle: json['documentTitle'] as String,
      documentUrl: json['documentUrl'] as String?,
      recipientName: json['recipientName'] as String,
      recipientEmail: json['recipientEmail'] as String,
      recipientCompanyId: json['recipientCompanyId'] as String?,
      recipientCompanyName: json['recipientCompanyName'] as String?,
      message: json['message'] as String?,
      status: $enumDecodeNullable(_$ShareStatusEnumMap, json['status']) ??
          ShareStatus.pending,
      createdAt:
          const TimestampConverter().fromJson(json['createdAt'] as Timestamp),
      sentAt: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['sentAt'], const TimestampConverter().fromJson),
      openedAt: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['openedAt'], const TimestampConverter().fromJson),
      signedAt: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['signedAt'], const TimestampConverter().fromJson),
      createdBy: json['createdBy'] as String?,
      organizationId: json['organizationId'] as String?,
      signatureLink: json['signatureLink'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$DocumentShareImplToJson(_$DocumentShareImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'reportId': instance.reportId,
      'projectId': instance.projectId,
      'documentType': _$DocumentTypeEnumMap[instance.documentType]!,
      'documentTitle': instance.documentTitle,
      'documentUrl': instance.documentUrl,
      'recipientName': instance.recipientName,
      'recipientEmail': instance.recipientEmail,
      'recipientCompanyId': instance.recipientCompanyId,
      'recipientCompanyName': instance.recipientCompanyName,
      'message': instance.message,
      'status': _$ShareStatusEnumMap[instance.status]!,
      'createdAt': const TimestampConverter().toJson(instance.createdAt),
      'sentAt': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.sentAt, const TimestampConverter().toJson),
      'openedAt': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.openedAt, const TimestampConverter().toJson),
      'signedAt': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.signedAt, const TimestampConverter().toJson),
      'createdBy': instance.createdBy,
      'organizationId': instance.organizationId,
      'signatureLink': instance.signatureLink,
      'isDeleted': instance.isDeleted,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.acceptanceReport: 'acceptanceReport',
  DocumentType.clearanceReceipt: 'clearanceReceipt',
  DocumentType.sitePlan: 'sitePlan',
  DocumentType.other: 'other',
};

const _$ShareStatusEnumMap = {
  ShareStatus.pending: 'pending',
  ShareStatus.sent: 'sent',
  ShareStatus.opened: 'opened',
  ShareStatus.signed: 'signed',
  ShareStatus.error: 'error',
};

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
