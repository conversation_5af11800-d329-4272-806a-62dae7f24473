// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reservation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Reservation _$ReservationFromJson(Map<String, dynamic> json) {
  return _Reservation.fromJson(json);
}

/// @nodoc
mixin _$Reservation {
  /// Identifiant unique de la réservation
  String get id => throw _privateConstructorUsedError;

  /// Description de la réservation
  String get description => throw _privateConstructorUsedError;

  /// Localisation du défaut
  String get location => throw _privateConstructorUsedError;

  /// Identifiant de l'entreprise responsable
  String? get assignedCompanyId => throw _privateConstructorUsedError;

  /// Nom de l'entreprise responsable
  String? get assignedCompanyName => throw _privateConstructorUsedError;

  /// Date limite pour corriger le défaut
  @TimestampConverter()
  DateTime? get dueDate => throw _privateConstructorUsedError;

  /// Statut de la réservation (ouverte, en cours, résolue)
  String get status => throw _privateConstructorUsedError;

  /// Date de création de la réservation
  @TimestampConverter()
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Date de dernière modification de la réservation
  @TimestampConverter()
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Date de résolution de la réservation
  @TimestampConverter()
  DateTime? get resolvedAt => throw _privateConstructorUsedError;

  /// Liste des URLs des photos du défaut
  List<String> get photos => throw _privateConstructorUsedError;

  /// Commentaires additionnels
  String? get comments => throw _privateConstructorUsedError;

  /// Identifiant de la tâche associée dans le planning
  String? get linkedTaskId => throw _privateConstructorUsedError;

  /// Priorité de la réservation (basse, moyenne, haute)
  String get priority => throw _privateConstructorUsedError;

  /// Catégorie de la réservation
  String? get category => throw _privateConstructorUsedError;

  /// Serializes this Reservation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Reservation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReservationCopyWith<Reservation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReservationCopyWith<$Res> {
  factory $ReservationCopyWith(
          Reservation value, $Res Function(Reservation) then) =
      _$ReservationCopyWithImpl<$Res, Reservation>;
  @useResult
  $Res call(
      {String id,
      String description,
      String location,
      String? assignedCompanyId,
      String? assignedCompanyName,
      @TimestampConverter() DateTime? dueDate,
      String status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime updatedAt,
      @TimestampConverter() DateTime? resolvedAt,
      List<String> photos,
      String? comments,
      String? linkedTaskId,
      String priority,
      String? category});
}

/// @nodoc
class _$ReservationCopyWithImpl<$Res, $Val extends Reservation>
    implements $ReservationCopyWith<$Res> {
  _$ReservationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Reservation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? description = null,
    Object? location = null,
    Object? assignedCompanyId = freezed,
    Object? assignedCompanyName = freezed,
    Object? dueDate = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? resolvedAt = freezed,
    Object? photos = null,
    Object? comments = freezed,
    Object? linkedTaskId = freezed,
    Object? priority = null,
    Object? category = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      assignedCompanyId: freezed == assignedCompanyId
          ? _value.assignedCompanyId
          : assignedCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedCompanyName: freezed == assignedCompanyName
          ? _value.assignedCompanyName
          : assignedCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      resolvedAt: freezed == resolvedAt
          ? _value.resolvedAt
          : resolvedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      photos: null == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedTaskId: freezed == linkedTaskId
          ? _value.linkedTaskId
          : linkedTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReservationImplCopyWith<$Res>
    implements $ReservationCopyWith<$Res> {
  factory _$$ReservationImplCopyWith(
          _$ReservationImpl value, $Res Function(_$ReservationImpl) then) =
      __$$ReservationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String description,
      String location,
      String? assignedCompanyId,
      String? assignedCompanyName,
      @TimestampConverter() DateTime? dueDate,
      String status,
      @TimestampConverter() DateTime createdAt,
      @TimestampConverter() DateTime updatedAt,
      @TimestampConverter() DateTime? resolvedAt,
      List<String> photos,
      String? comments,
      String? linkedTaskId,
      String priority,
      String? category});
}

/// @nodoc
class __$$ReservationImplCopyWithImpl<$Res>
    extends _$ReservationCopyWithImpl<$Res, _$ReservationImpl>
    implements _$$ReservationImplCopyWith<$Res> {
  __$$ReservationImplCopyWithImpl(
      _$ReservationImpl _value, $Res Function(_$ReservationImpl) _then)
      : super(_value, _then);

  /// Create a copy of Reservation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? description = null,
    Object? location = null,
    Object? assignedCompanyId = freezed,
    Object? assignedCompanyName = freezed,
    Object? dueDate = freezed,
    Object? status = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? resolvedAt = freezed,
    Object? photos = null,
    Object? comments = freezed,
    Object? linkedTaskId = freezed,
    Object? priority = null,
    Object? category = freezed,
  }) {
    return _then(_$ReservationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      assignedCompanyId: freezed == assignedCompanyId
          ? _value.assignedCompanyId
          : assignedCompanyId // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedCompanyName: freezed == assignedCompanyName
          ? _value.assignedCompanyName
          : assignedCompanyName // ignore: cast_nullable_to_non_nullable
              as String?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      resolvedAt: freezed == resolvedAt
          ? _value.resolvedAt
          : resolvedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      photos: null == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedTaskId: freezed == linkedTaskId
          ? _value.linkedTaskId
          : linkedTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
class _$ReservationImpl extends _Reservation {
  const _$ReservationImpl(
      {required this.id,
      required this.description,
      required this.location,
      this.assignedCompanyId,
      this.assignedCompanyName,
      @TimestampConverter() this.dueDate,
      this.status = 'open',
      @TimestampConverter() required this.createdAt,
      @TimestampConverter() required this.updatedAt,
      @TimestampConverter() this.resolvedAt,
      final List<String> photos = const [],
      this.comments,
      this.linkedTaskId,
      this.priority = 'medium',
      this.category})
      : _photos = photos,
        super._();

  factory _$ReservationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReservationImplFromJson(json);

  /// Identifiant unique de la réservation
  @override
  final String id;

  /// Description de la réservation
  @override
  final String description;

  /// Localisation du défaut
  @override
  final String location;

  /// Identifiant de l'entreprise responsable
  @override
  final String? assignedCompanyId;

  /// Nom de l'entreprise responsable
  @override
  final String? assignedCompanyName;

  /// Date limite pour corriger le défaut
  @override
  @TimestampConverter()
  final DateTime? dueDate;

  /// Statut de la réservation (ouverte, en cours, résolue)
  @override
  @JsonKey()
  final String status;

  /// Date de création de la réservation
  @override
  @TimestampConverter()
  final DateTime createdAt;

  /// Date de dernière modification de la réservation
  @override
  @TimestampConverter()
  final DateTime updatedAt;

  /// Date de résolution de la réservation
  @override
  @TimestampConverter()
  final DateTime? resolvedAt;

  /// Liste des URLs des photos du défaut
  final List<String> _photos;

  /// Liste des URLs des photos du défaut
  @override
  @JsonKey()
  List<String> get photos {
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photos);
  }

  /// Commentaires additionnels
  @override
  final String? comments;

  /// Identifiant de la tâche associée dans le planning
  @override
  final String? linkedTaskId;

  /// Priorité de la réservation (basse, moyenne, haute)
  @override
  @JsonKey()
  final String priority;

  /// Catégorie de la réservation
  @override
  final String? category;

  @override
  String toString() {
    return 'Reservation(id: $id, description: $description, location: $location, assignedCompanyId: $assignedCompanyId, assignedCompanyName: $assignedCompanyName, dueDate: $dueDate, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, resolvedAt: $resolvedAt, photos: $photos, comments: $comments, linkedTaskId: $linkedTaskId, priority: $priority, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReservationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.assignedCompanyId, assignedCompanyId) ||
                other.assignedCompanyId == assignedCompanyId) &&
            (identical(other.assignedCompanyName, assignedCompanyName) ||
                other.assignedCompanyName == assignedCompanyName) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.resolvedAt, resolvedAt) ||
                other.resolvedAt == resolvedAt) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.comments, comments) ||
                other.comments == comments) &&
            (identical(other.linkedTaskId, linkedTaskId) ||
                other.linkedTaskId == linkedTaskId) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.category, category) ||
                other.category == category));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      description,
      location,
      assignedCompanyId,
      assignedCompanyName,
      dueDate,
      status,
      createdAt,
      updatedAt,
      resolvedAt,
      const DeepCollectionEquality().hash(_photos),
      comments,
      linkedTaskId,
      priority,
      category);

  /// Create a copy of Reservation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReservationImplCopyWith<_$ReservationImpl> get copyWith =>
      __$$ReservationImplCopyWithImpl<_$ReservationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReservationImplToJson(
      this,
    );
  }
}

abstract class _Reservation extends Reservation {
  const factory _Reservation(
      {required final String id,
      required final String description,
      required final String location,
      final String? assignedCompanyId,
      final String? assignedCompanyName,
      @TimestampConverter() final DateTime? dueDate,
      final String status,
      @TimestampConverter() required final DateTime createdAt,
      @TimestampConverter() required final DateTime updatedAt,
      @TimestampConverter() final DateTime? resolvedAt,
      final List<String> photos,
      final String? comments,
      final String? linkedTaskId,
      final String priority,
      final String? category}) = _$ReservationImpl;
  const _Reservation._() : super._();

  factory _Reservation.fromJson(Map<String, dynamic> json) =
      _$ReservationImpl.fromJson;

  /// Identifiant unique de la réservation
  @override
  String get id;

  /// Description de la réservation
  @override
  String get description;

  /// Localisation du défaut
  @override
  String get location;

  /// Identifiant de l'entreprise responsable
  @override
  String? get assignedCompanyId;

  /// Nom de l'entreprise responsable
  @override
  String? get assignedCompanyName;

  /// Date limite pour corriger le défaut
  @override
  @TimestampConverter()
  DateTime? get dueDate;

  /// Statut de la réservation (ouverte, en cours, résolue)
  @override
  String get status;

  /// Date de création de la réservation
  @override
  @TimestampConverter()
  DateTime get createdAt;

  /// Date de dernière modification de la réservation
  @override
  @TimestampConverter()
  DateTime get updatedAt;

  /// Date de résolution de la réservation
  @override
  @TimestampConverter()
  DateTime? get resolvedAt;

  /// Liste des URLs des photos du défaut
  @override
  List<String> get photos;

  /// Commentaires additionnels
  @override
  String? get comments;

  /// Identifiant de la tâche associée dans le planning
  @override
  String? get linkedTaskId;

  /// Priorité de la réservation (basse, moyenne, haute)
  @override
  String get priority;

  /// Catégorie de la réservation
  @override
  String? get category;

  /// Create a copy of Reservation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReservationImplCopyWith<_$ReservationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
