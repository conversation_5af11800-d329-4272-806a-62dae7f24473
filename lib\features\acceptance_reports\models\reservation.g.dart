// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reservation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReservationImpl _$$ReservationImplFromJson(Map<String, dynamic> json) =>
    _$ReservationImpl(
      id: json['id'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      assignedCompanyId: json['assigned_company_id'] as String?,
      assignedCompanyName: json['assigned_company_name'] as String?,
      dueDate: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['due_date'], const TimestampConverter().fromJson),
      status: json['status'] as String? ?? 'open',
      createdAt:
          const TimestampConverter().fromJson(json['created_at'] as Timestamp),
      updatedAt:
          const TimestampConverter().fromJson(json['updated_at'] as Timestamp),
      resolvedAt: _$JsonConverterFromJson<Timestamp, DateTime>(
          json['resolved_at'], const TimestampConverter().fromJson),
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      comments: json['comments'] as String?,
      linkedTaskId: json['linked_task_id'] as String?,
      priority: json['priority'] as String? ?? 'medium',
      category: json['category'] as String?,
    );

Map<String, dynamic> _$$ReservationImplToJson(_$ReservationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'location': instance.location,
      'assigned_company_id': instance.assignedCompanyId,
      'assigned_company_name': instance.assignedCompanyName,
      'due_date': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.dueDate, const TimestampConverter().toJson),
      'status': instance.status,
      'created_at': const TimestampConverter().toJson(instance.createdAt),
      'updated_at': const TimestampConverter().toJson(instance.updatedAt),
      'resolved_at': _$JsonConverterToJson<Timestamp, DateTime>(
          instance.resolvedAt, const TimestampConverter().toJson),
      'photos': instance.photos,
      'comments': instance.comments,
      'linked_task_id': instance.linkedTaskId,
      'priority': instance.priority,
      'category': instance.category,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
