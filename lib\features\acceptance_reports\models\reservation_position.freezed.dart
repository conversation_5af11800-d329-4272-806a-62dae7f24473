// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reservation_position.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReservationPosition _$ReservationPositionFromJson(Map<String, dynamic> json) {
  return _ReservationPosition.fromJson(json);
}

/// @nodoc
mixin _$ReservationPosition {
  String get reservationId => throw _privateConstructorUsedError;
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;
  int get color =>
      throw _privateConstructorUsedError; // Rose par défaut pour les réservations
  String? get note => throw _privateConstructorUsedError;

  /// Serializes this ReservationPosition to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReservationPosition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReservationPositionCopyWith<ReservationPosition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReservationPositionCopyWith<$Res> {
  factory $ReservationPositionCopyWith(
          ReservationPosition value, $Res Function(ReservationPosition) then) =
      _$ReservationPositionCopyWithImpl<$Res, ReservationPosition>;
  @useResult
  $Res call(
      {String reservationId, double x, double y, int color, String? note});
}

/// @nodoc
class _$ReservationPositionCopyWithImpl<$Res, $Val extends ReservationPosition>
    implements $ReservationPositionCopyWith<$Res> {
  _$ReservationPositionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReservationPosition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reservationId = null,
    Object? x = null,
    Object? y = null,
    Object? color = null,
    Object? note = freezed,
  }) {
    return _then(_value.copyWith(
      reservationId: null == reservationId
          ? _value.reservationId
          : reservationId // ignore: cast_nullable_to_non_nullable
              as String,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as int,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReservationPositionImplCopyWith<$Res>
    implements $ReservationPositionCopyWith<$Res> {
  factory _$$ReservationPositionImplCopyWith(_$ReservationPositionImpl value,
          $Res Function(_$ReservationPositionImpl) then) =
      __$$ReservationPositionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String reservationId, double x, double y, int color, String? note});
}

/// @nodoc
class __$$ReservationPositionImplCopyWithImpl<$Res>
    extends _$ReservationPositionCopyWithImpl<$Res, _$ReservationPositionImpl>
    implements _$$ReservationPositionImplCopyWith<$Res> {
  __$$ReservationPositionImplCopyWithImpl(_$ReservationPositionImpl _value,
      $Res Function(_$ReservationPositionImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationPosition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reservationId = null,
    Object? x = null,
    Object? y = null,
    Object? color = null,
    Object? note = freezed,
  }) {
    return _then(_$ReservationPositionImpl(
      reservationId: null == reservationId
          ? _value.reservationId
          : reservationId // ignore: cast_nullable_to_non_nullable
              as String,
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as int,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReservationPositionImpl implements _ReservationPosition {
  const _$ReservationPositionImpl(
      {required this.reservationId,
      required this.x,
      required this.y,
      this.color = 0xFFE91E63,
      this.note});

  factory _$ReservationPositionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReservationPositionImplFromJson(json);

  @override
  final String reservationId;
  @override
  final double x;
  @override
  final double y;
  @override
  @JsonKey()
  final int color;
// Rose par défaut pour les réservations
  @override
  final String? note;

  @override
  String toString() {
    return 'ReservationPosition(reservationId: $reservationId, x: $x, y: $y, color: $color, note: $note)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReservationPositionImpl &&
            (identical(other.reservationId, reservationId) ||
                other.reservationId == reservationId) &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, reservationId, x, y, color, note);

  /// Create a copy of ReservationPosition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReservationPositionImplCopyWith<_$ReservationPositionImpl> get copyWith =>
      __$$ReservationPositionImplCopyWithImpl<_$ReservationPositionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReservationPositionImplToJson(
      this,
    );
  }
}

abstract class _ReservationPosition implements ReservationPosition {
  const factory _ReservationPosition(
      {required final String reservationId,
      required final double x,
      required final double y,
      final int color,
      final String? note}) = _$ReservationPositionImpl;

  factory _ReservationPosition.fromJson(Map<String, dynamic> json) =
      _$ReservationPositionImpl.fromJson;

  @override
  String get reservationId;
  @override
  double get x;
  @override
  double get y;
  @override
  int get color; // Rose par défaut pour les réservations
  @override
  String? get note;

  /// Create a copy of ReservationPosition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReservationPositionImplCopyWith<_$ReservationPositionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
