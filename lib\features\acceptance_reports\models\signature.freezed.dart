// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signature.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Signature _$SignatureFromJson(Map<String, dynamic> json) {
  return _Signature.fromJson(json);
}

/// @nodoc
mixin _$Signature {
  /// Identifiant unique de la signature
  String get id => throw _privateConstructorUsedError;

  /// Nom du signataire
  String get signerName => throw _privateConstructorUsedError;

  /// Rôle du signataire
  String get signerRole => throw _privateConstructorUsedError;

  /// Entreprise du signataire
  String? get signerCompany => throw _privateConstructorUsedError;

  /// URL de l'image de la signature
  String get signatureImageUrl => throw _privateConstructorUsedError;

  /// Date de la signature
  @TimestampConverter()
  DateTime get signedAt => throw _privateConstructorUsedError;

  /// Adresse IP du signataire (pour audit)
  String? get ipAddress => throw _privateConstructorUsedError;

  /// Identifiant de l'utilisateur qui a signé
  String? get userId => throw _privateConstructorUsedError;

  /// Commentaires additionnels
  String? get comments => throw _privateConstructorUsedError;

  /// Indique si la signature est valide
  bool get isValid => throw _privateConstructorUsedError;

  /// Serializes this Signature to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Signature
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SignatureCopyWith<Signature> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignatureCopyWith<$Res> {
  factory $SignatureCopyWith(Signature value, $Res Function(Signature) then) =
      _$SignatureCopyWithImpl<$Res, Signature>;
  @useResult
  $Res call(
      {String id,
      String signerName,
      String signerRole,
      String? signerCompany,
      String signatureImageUrl,
      @TimestampConverter() DateTime signedAt,
      String? ipAddress,
      String? userId,
      String? comments,
      bool isValid});
}

/// @nodoc
class _$SignatureCopyWithImpl<$Res, $Val extends Signature>
    implements $SignatureCopyWith<$Res> {
  _$SignatureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Signature
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? signerName = null,
    Object? signerRole = null,
    Object? signerCompany = freezed,
    Object? signatureImageUrl = null,
    Object? signedAt = null,
    Object? ipAddress = freezed,
    Object? userId = freezed,
    Object? comments = freezed,
    Object? isValid = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      signerName: null == signerName
          ? _value.signerName
          : signerName // ignore: cast_nullable_to_non_nullable
              as String,
      signerRole: null == signerRole
          ? _value.signerRole
          : signerRole // ignore: cast_nullable_to_non_nullable
              as String,
      signerCompany: freezed == signerCompany
          ? _value.signerCompany
          : signerCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageUrl: null == signatureImageUrl
          ? _value.signatureImageUrl
          : signatureImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      signedAt: null == signedAt
          ? _value.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignatureImplCopyWith<$Res>
    implements $SignatureCopyWith<$Res> {
  factory _$$SignatureImplCopyWith(
          _$SignatureImpl value, $Res Function(_$SignatureImpl) then) =
      __$$SignatureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String signerName,
      String signerRole,
      String? signerCompany,
      String signatureImageUrl,
      @TimestampConverter() DateTime signedAt,
      String? ipAddress,
      String? userId,
      String? comments,
      bool isValid});
}

/// @nodoc
class __$$SignatureImplCopyWithImpl<$Res>
    extends _$SignatureCopyWithImpl<$Res, _$SignatureImpl>
    implements _$$SignatureImplCopyWith<$Res> {
  __$$SignatureImplCopyWithImpl(
      _$SignatureImpl _value, $Res Function(_$SignatureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Signature
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? signerName = null,
    Object? signerRole = null,
    Object? signerCompany = freezed,
    Object? signatureImageUrl = null,
    Object? signedAt = null,
    Object? ipAddress = freezed,
    Object? userId = freezed,
    Object? comments = freezed,
    Object? isValid = null,
  }) {
    return _then(_$SignatureImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      signerName: null == signerName
          ? _value.signerName
          : signerName // ignore: cast_nullable_to_non_nullable
              as String,
      signerRole: null == signerRole
          ? _value.signerRole
          : signerRole // ignore: cast_nullable_to_non_nullable
              as String,
      signerCompany: freezed == signerCompany
          ? _value.signerCompany
          : signerCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageUrl: null == signatureImageUrl
          ? _value.signatureImageUrl
          : signatureImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      signedAt: null == signedAt
          ? _value.signedAt
          : signedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
class _$SignatureImpl extends _Signature {
  const _$SignatureImpl(
      {required this.id,
      required this.signerName,
      required this.signerRole,
      this.signerCompany,
      required this.signatureImageUrl,
      @TimestampConverter() required this.signedAt,
      this.ipAddress,
      this.userId,
      this.comments,
      this.isValid = true})
      : super._();

  factory _$SignatureImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignatureImplFromJson(json);

  /// Identifiant unique de la signature
  @override
  final String id;

  /// Nom du signataire
  @override
  final String signerName;

  /// Rôle du signataire
  @override
  final String signerRole;

  /// Entreprise du signataire
  @override
  final String? signerCompany;

  /// URL de l'image de la signature
  @override
  final String signatureImageUrl;

  /// Date de la signature
  @override
  @TimestampConverter()
  final DateTime signedAt;

  /// Adresse IP du signataire (pour audit)
  @override
  final String? ipAddress;

  /// Identifiant de l'utilisateur qui a signé
  @override
  final String? userId;

  /// Commentaires additionnels
  @override
  final String? comments;

  /// Indique si la signature est valide
  @override
  @JsonKey()
  final bool isValid;

  @override
  String toString() {
    return 'Signature(id: $id, signerName: $signerName, signerRole: $signerRole, signerCompany: $signerCompany, signatureImageUrl: $signatureImageUrl, signedAt: $signedAt, ipAddress: $ipAddress, userId: $userId, comments: $comments, isValid: $isValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignatureImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.signerName, signerName) ||
                other.signerName == signerName) &&
            (identical(other.signerRole, signerRole) ||
                other.signerRole == signerRole) &&
            (identical(other.signerCompany, signerCompany) ||
                other.signerCompany == signerCompany) &&
            (identical(other.signatureImageUrl, signatureImageUrl) ||
                other.signatureImageUrl == signatureImageUrl) &&
            (identical(other.signedAt, signedAt) ||
                other.signedAt == signedAt) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.comments, comments) ||
                other.comments == comments) &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      signerName,
      signerRole,
      signerCompany,
      signatureImageUrl,
      signedAt,
      ipAddress,
      userId,
      comments,
      isValid);

  /// Create a copy of Signature
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignatureImplCopyWith<_$SignatureImpl> get copyWith =>
      __$$SignatureImplCopyWithImpl<_$SignatureImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SignatureImplToJson(
      this,
    );
  }
}

abstract class _Signature extends Signature {
  const factory _Signature(
      {required final String id,
      required final String signerName,
      required final String signerRole,
      final String? signerCompany,
      required final String signatureImageUrl,
      @TimestampConverter() required final DateTime signedAt,
      final String? ipAddress,
      final String? userId,
      final String? comments,
      final bool isValid}) = _$SignatureImpl;
  const _Signature._() : super._();

  factory _Signature.fromJson(Map<String, dynamic> json) =
      _$SignatureImpl.fromJson;

  /// Identifiant unique de la signature
  @override
  String get id;

  /// Nom du signataire
  @override
  String get signerName;

  /// Rôle du signataire
  @override
  String get signerRole;

  /// Entreprise du signataire
  @override
  String? get signerCompany;

  /// URL de l'image de la signature
  @override
  String get signatureImageUrl;

  /// Date de la signature
  @override
  @TimestampConverter()
  DateTime get signedAt;

  /// Adresse IP du signataire (pour audit)
  @override
  String? get ipAddress;

  /// Identifiant de l'utilisateur qui a signé
  @override
  String? get userId;

  /// Commentaires additionnels
  @override
  String? get comments;

  /// Indique si la signature est valide
  @override
  bool get isValid;

  /// Create a copy of Signature
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignatureImplCopyWith<_$SignatureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
