// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signature.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SignatureImpl _$$SignatureImplFromJson(Map<String, dynamic> json) =>
    _$SignatureImpl(
      id: json['id'] as String,
      signerName: json['signer_name'] as String,
      signerRole: json['signer_role'] as String,
      signerCompany: json['signer_company'] as String?,
      signatureImageUrl: json['signature_image_url'] as String,
      signedAt:
          const TimestampConverter().fromJson(json['signed_at'] as Timestamp),
      ipAddress: json['ip_address'] as String?,
      userId: json['user_id'] as String?,
      comments: json['comments'] as String?,
      isValid: json['is_valid'] as bool? ?? true,
    );

Map<String, dynamic> _$$SignatureImplToJson(_$SignatureImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'signer_name': instance.signerName,
      'signer_role': instance.signerRole,
      'signer_company': instance.signerCompany,
      'signature_image_url': instance.signatureImageUrl,
      'signed_at': const TimestampConverter().toJson(instance.signedAt),
      'ip_address': instance.ipAddress,
      'user_id': instance.userId,
      'comments': instance.comments,
      'is_valid': instance.isValid,
    };
