import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'planning_task.freezed.dart';
part 'planning_task.g.dart';

/// Statut d'une tâche dans le planning
enum TaskStatus {
  @JsonValue(0)
  ToDo,
  @JsonValue(1)
  InProgress,
  @JsonValue(2)
  Done,
  @JsonValue(3)
  Blocked,

  // Statuts spécifiques MOE (Maîtrise d'Œuvre)
  @JsonValue('Esquisse')
  Esquisse, // 15%
  @JsonValue('AvantProjetSommaire')
  AvantProjetSommaire, // 25%
  @JsonValue('AvantProjetDefinitif')
  AvantProjetDefinitif, // 35%
  @JsonValue('DossierConsultation')
  DossierConsultation, // 45%
  @JsonValue('AnalyseOffres')
  AnalyseOffres, // 55%
  @JsonValue('SuiviChantier')
  SuiviChantier, // 70%
  @JsonValue('Reception')
  Reception, // 90%
}

/// Modèle représentant une tâche dans le planning
@freezed
class PlanningTask with _$PlanningTask {
  factory PlanningTask({
    required String id,
    required String taskName,
    required String planningId,
    String? description,
    String? assignedTo,
    String? colorIndex,
    DateTime? startDate,
    DateTime? endDate,
    @Default(0) int progressPercentage,
    @Default(TaskStatus.ToDo) TaskStatus status,
    @JsonKey(ignore: true) List<String>? dependencies,
    @JsonKey(ignore: true) List<String>? subtasks,
    String? parentTaskId,
    @Default(false) bool isMilestone,
    @Default(false) bool isCritical,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _PlanningTask;

  factory PlanningTask.fromJson(Map<String, dynamic> json) =>
      _$PlanningTaskFromJson(json);

  /// Convertit un Timestamp Firestore en DateTime
  static DateTime? _timestampToDateTime(Timestamp? timestamp) {
    return timestamp?.toDate();
  }

  /// Convertit une DateTime en Timestamp Firestore
  static Timestamp? _dateTimeToTimestamp(DateTime? dateTime) {
    return dateTime != null ? Timestamp.fromDate(dateTime) : null;
  }
}
