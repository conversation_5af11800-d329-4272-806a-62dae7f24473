// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'planning_task.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PlanningTask _$PlanningTaskFromJson(Map<String, dynamic> json) {
  return _PlanningTask.fromJson(json);
}

/// @nodoc
mixin _$PlanningTask {
  String get id => throw _privateConstructorUsedError;
  String get taskName => throw _privateConstructorUsedError;
  String get planningId => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get assignedTo => throw _privateConstructorUsedError;
  String? get colorIndex => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  int get progressPercentage => throw _privateConstructorUsedError;
  TaskStatus get status => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  List<String>? get dependencies => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  List<String>? get subtasks => throw _privateConstructorUsedError;
  String? get parentTaskId => throw _privateConstructorUsedError;
  bool get isMilestone => throw _privateConstructorUsedError;
  bool get isCritical => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this PlanningTask to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PlanningTaskCopyWith<PlanningTask> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanningTaskCopyWith<$Res> {
  factory $PlanningTaskCopyWith(
          PlanningTask value, $Res Function(PlanningTask) then) =
      _$PlanningTaskCopyWithImpl<$Res, PlanningTask>;
  @useResult
  $Res call(
      {String id,
      String taskName,
      String planningId,
      String? description,
      String? assignedTo,
      String? colorIndex,
      DateTime? startDate,
      DateTime? endDate,
      int progressPercentage,
      TaskStatus status,
      @JsonKey(ignore: true) List<String>? dependencies,
      @JsonKey(ignore: true) List<String>? subtasks,
      String? parentTaskId,
      bool isMilestone,
      bool isCritical,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$PlanningTaskCopyWithImpl<$Res, $Val extends PlanningTask>
    implements $PlanningTaskCopyWith<$Res> {
  _$PlanningTaskCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? taskName = null,
    Object? planningId = null,
    Object? description = freezed,
    Object? assignedTo = freezed,
    Object? colorIndex = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? progressPercentage = null,
    Object? status = null,
    Object? dependencies = freezed,
    Object? subtasks = freezed,
    Object? parentTaskId = freezed,
    Object? isMilestone = null,
    Object? isCritical = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      taskName: null == taskName
          ? _value.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String,
      planningId: null == planningId
          ? _value.planningId
          : planningId // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedTo: freezed == assignedTo
          ? _value.assignedTo
          : assignedTo // ignore: cast_nullable_to_non_nullable
              as String?,
      colorIndex: freezed == colorIndex
          ? _value.colorIndex
          : colorIndex // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus,
      dependencies: freezed == dependencies
          ? _value.dependencies
          : dependencies // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subtasks: freezed == subtasks
          ? _value.subtasks
          : subtasks // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentTaskId: freezed == parentTaskId
          ? _value.parentTaskId
          : parentTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      isMilestone: null == isMilestone
          ? _value.isMilestone
          : isMilestone // ignore: cast_nullable_to_non_nullable
              as bool,
      isCritical: null == isCritical
          ? _value.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PlanningTaskImplCopyWith<$Res>
    implements $PlanningTaskCopyWith<$Res> {
  factory _$$PlanningTaskImplCopyWith(
          _$PlanningTaskImpl value, $Res Function(_$PlanningTaskImpl) then) =
      __$$PlanningTaskImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String taskName,
      String planningId,
      String? description,
      String? assignedTo,
      String? colorIndex,
      DateTime? startDate,
      DateTime? endDate,
      int progressPercentage,
      TaskStatus status,
      @JsonKey(ignore: true) List<String>? dependencies,
      @JsonKey(ignore: true) List<String>? subtasks,
      String? parentTaskId,
      bool isMilestone,
      bool isCritical,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$PlanningTaskImplCopyWithImpl<$Res>
    extends _$PlanningTaskCopyWithImpl<$Res, _$PlanningTaskImpl>
    implements _$$PlanningTaskImplCopyWith<$Res> {
  __$$PlanningTaskImplCopyWithImpl(
      _$PlanningTaskImpl _value, $Res Function(_$PlanningTaskImpl) _then)
      : super(_value, _then);

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? taskName = null,
    Object? planningId = null,
    Object? description = freezed,
    Object? assignedTo = freezed,
    Object? colorIndex = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? progressPercentage = null,
    Object? status = null,
    Object? dependencies = freezed,
    Object? subtasks = freezed,
    Object? parentTaskId = freezed,
    Object? isMilestone = null,
    Object? isCritical = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$PlanningTaskImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      taskName: null == taskName
          ? _value.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String,
      planningId: null == planningId
          ? _value.planningId
          : planningId // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedTo: freezed == assignedTo
          ? _value.assignedTo
          : assignedTo // ignore: cast_nullable_to_non_nullable
              as String?,
      colorIndex: freezed == colorIndex
          ? _value.colorIndex
          : colorIndex // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus,
      dependencies: freezed == dependencies
          ? _value._dependencies
          : dependencies // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subtasks: freezed == subtasks
          ? _value._subtasks
          : subtasks // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentTaskId: freezed == parentTaskId
          ? _value.parentTaskId
          : parentTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      isMilestone: null == isMilestone
          ? _value.isMilestone
          : isMilestone // ignore: cast_nullable_to_non_nullable
              as bool,
      isCritical: null == isCritical
          ? _value.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PlanningTaskImpl implements _PlanningTask {
  _$PlanningTaskImpl(
      {required this.id,
      required this.taskName,
      required this.planningId,
      this.description,
      this.assignedTo,
      this.colorIndex,
      this.startDate,
      this.endDate,
      this.progressPercentage = 0,
      this.status = TaskStatus.ToDo,
      @JsonKey(ignore: true) final List<String>? dependencies,
      @JsonKey(ignore: true) final List<String>? subtasks,
      this.parentTaskId,
      this.isMilestone = false,
      this.isCritical = false,
      this.createdAt,
      this.updatedAt})
      : _dependencies = dependencies,
        _subtasks = subtasks;

  factory _$PlanningTaskImpl.fromJson(Map<String, dynamic> json) =>
      _$$PlanningTaskImplFromJson(json);

  @override
  final String id;
  @override
  final String taskName;
  @override
  final String planningId;
  @override
  final String? description;
  @override
  final String? assignedTo;
  @override
  final String? colorIndex;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey()
  final int progressPercentage;
  @override
  @JsonKey()
  final TaskStatus status;
  final List<String>? _dependencies;
  @override
  @JsonKey(ignore: true)
  List<String>? get dependencies {
    final value = _dependencies;
    if (value == null) return null;
    if (_dependencies is EqualUnmodifiableListView) return _dependencies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _subtasks;
  @override
  @JsonKey(ignore: true)
  List<String>? get subtasks {
    final value = _subtasks;
    if (value == null) return null;
    if (_subtasks is EqualUnmodifiableListView) return _subtasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? parentTaskId;
  @override
  @JsonKey()
  final bool isMilestone;
  @override
  @JsonKey()
  final bool isCritical;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'PlanningTask(id: $id, taskName: $taskName, planningId: $planningId, description: $description, assignedTo: $assignedTo, colorIndex: $colorIndex, startDate: $startDate, endDate: $endDate, progressPercentage: $progressPercentage, status: $status, dependencies: $dependencies, subtasks: $subtasks, parentTaskId: $parentTaskId, isMilestone: $isMilestone, isCritical: $isCritical, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlanningTaskImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.taskName, taskName) ||
                other.taskName == taskName) &&
            (identical(other.planningId, planningId) ||
                other.planningId == planningId) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.assignedTo, assignedTo) ||
                other.assignedTo == assignedTo) &&
            (identical(other.colorIndex, colorIndex) ||
                other.colorIndex == colorIndex) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality()
                .equals(other._dependencies, _dependencies) &&
            const DeepCollectionEquality().equals(other._subtasks, _subtasks) &&
            (identical(other.parentTaskId, parentTaskId) ||
                other.parentTaskId == parentTaskId) &&
            (identical(other.isMilestone, isMilestone) ||
                other.isMilestone == isMilestone) &&
            (identical(other.isCritical, isCritical) ||
                other.isCritical == isCritical) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      taskName,
      planningId,
      description,
      assignedTo,
      colorIndex,
      startDate,
      endDate,
      progressPercentage,
      status,
      const DeepCollectionEquality().hash(_dependencies),
      const DeepCollectionEquality().hash(_subtasks),
      parentTaskId,
      isMilestone,
      isCritical,
      createdAt,
      updatedAt);

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PlanningTaskImplCopyWith<_$PlanningTaskImpl> get copyWith =>
      __$$PlanningTaskImplCopyWithImpl<_$PlanningTaskImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PlanningTaskImplToJson(
      this,
    );
  }
}

abstract class _PlanningTask implements PlanningTask {
  factory _PlanningTask(
      {required final String id,
      required final String taskName,
      required final String planningId,
      final String? description,
      final String? assignedTo,
      final String? colorIndex,
      final DateTime? startDate,
      final DateTime? endDate,
      final int progressPercentage,
      final TaskStatus status,
      @JsonKey(ignore: true) final List<String>? dependencies,
      @JsonKey(ignore: true) final List<String>? subtasks,
      final String? parentTaskId,
      final bool isMilestone,
      final bool isCritical,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$PlanningTaskImpl;

  factory _PlanningTask.fromJson(Map<String, dynamic> json) =
      _$PlanningTaskImpl.fromJson;

  @override
  String get id;
  @override
  String get taskName;
  @override
  String get planningId;
  @override
  String? get description;
  @override
  String? get assignedTo;
  @override
  String? get colorIndex;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  int get progressPercentage;
  @override
  TaskStatus get status;
  @override
  @JsonKey(ignore: true)
  List<String>? get dependencies;
  @override
  @JsonKey(ignore: true)
  List<String>? get subtasks;
  @override
  String? get parentTaskId;
  @override
  bool get isMilestone;
  @override
  bool get isCritical;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PlanningTaskImplCopyWith<_$PlanningTaskImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
