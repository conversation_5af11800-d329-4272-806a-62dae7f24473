// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'planning_task.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PlanningTask {
  String get id;
  String get taskName;
  String get planningId;
  String? get description;
  String? get assignedTo;
  String? get colorIndex;
  DateTime? get startDate;
  DateTime? get endDate;
  int get progressPercentage;
  TaskStatus get status;
  @JsonKey(ignore: true)
  List<String>? get dependencies;
  @JsonKey(ignore: true)
  List<String>? get subtasks;
  String? get parentTaskId;
  bool get isMilestone;
  bool get isCritical;
  DateTime? get createdAt;
  DateTime? get updatedAt;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PlanningTaskCopyWith<PlanningTask> get copyWith =>
      _$PlanningTaskCopyWithImpl<PlanningTask>(
          this as PlanningTask, _$identity);

  /// Serializes this PlanningTask to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PlanningTask &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.taskName, taskName) ||
                other.taskName == taskName) &&
            (identical(other.planningId, planningId) ||
                other.planningId == planningId) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.assignedTo, assignedTo) ||
                other.assignedTo == assignedTo) &&
            (identical(other.colorIndex, colorIndex) ||
                other.colorIndex == colorIndex) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality()
                .equals(other.dependencies, dependencies) &&
            const DeepCollectionEquality().equals(other.subtasks, subtasks) &&
            (identical(other.parentTaskId, parentTaskId) ||
                other.parentTaskId == parentTaskId) &&
            (identical(other.isMilestone, isMilestone) ||
                other.isMilestone == isMilestone) &&
            (identical(other.isCritical, isCritical) ||
                other.isCritical == isCritical) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      taskName,
      planningId,
      description,
      assignedTo,
      colorIndex,
      startDate,
      endDate,
      progressPercentage,
      status,
      const DeepCollectionEquality().hash(dependencies),
      const DeepCollectionEquality().hash(subtasks),
      parentTaskId,
      isMilestone,
      isCritical,
      createdAt,
      updatedAt);

  @override
  String toString() {
    return 'PlanningTask(id: $id, taskName: $taskName, planningId: $planningId, description: $description, assignedTo: $assignedTo, colorIndex: $colorIndex, startDate: $startDate, endDate: $endDate, progressPercentage: $progressPercentage, status: $status, dependencies: $dependencies, subtasks: $subtasks, parentTaskId: $parentTaskId, isMilestone: $isMilestone, isCritical: $isCritical, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $PlanningTaskCopyWith<$Res> {
  factory $PlanningTaskCopyWith(
          PlanningTask value, $Res Function(PlanningTask) _then) =
      _$PlanningTaskCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String taskName,
      String planningId,
      String? description,
      String? assignedTo,
      String? colorIndex,
      DateTime? startDate,
      DateTime? endDate,
      int progressPercentage,
      TaskStatus status,
      @JsonKey(ignore: true) List<String>? dependencies,
      @JsonKey(ignore: true) List<String>? subtasks,
      String? parentTaskId,
      bool isMilestone,
      bool isCritical,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$PlanningTaskCopyWithImpl<$Res> implements $PlanningTaskCopyWith<$Res> {
  _$PlanningTaskCopyWithImpl(this._self, this._then);

  final PlanningTask _self;
  final $Res Function(PlanningTask) _then;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? taskName = null,
    Object? planningId = null,
    Object? description = freezed,
    Object? assignedTo = freezed,
    Object? colorIndex = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? progressPercentage = null,
    Object? status = null,
    Object? dependencies = freezed,
    Object? subtasks = freezed,
    Object? parentTaskId = freezed,
    Object? isMilestone = null,
    Object? isCritical = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      taskName: null == taskName
          ? _self.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String,
      planningId: null == planningId
          ? _self.planningId
          : planningId // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedTo: freezed == assignedTo
          ? _self.assignedTo
          : assignedTo // ignore: cast_nullable_to_non_nullable
              as String?,
      colorIndex: freezed == colorIndex
          ? _self.colorIndex
          : colorIndex // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _self.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus,
      dependencies: freezed == dependencies
          ? _self.dependencies
          : dependencies // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subtasks: freezed == subtasks
          ? _self.subtasks
          : subtasks // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentTaskId: freezed == parentTaskId
          ? _self.parentTaskId
          : parentTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      isMilestone: null == isMilestone
          ? _self.isMilestone
          : isMilestone // ignore: cast_nullable_to_non_nullable
              as bool,
      isCritical: null == isCritical
          ? _self.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PlanningTask implements PlanningTask {
  _PlanningTask(
      {required this.id,
      required this.taskName,
      required this.planningId,
      this.description,
      this.assignedTo,
      this.colorIndex,
      this.startDate,
      this.endDate,
      this.progressPercentage = 0,
      this.status = TaskStatus.ToDo,
      @JsonKey(ignore: true) final List<String>? dependencies,
      @JsonKey(ignore: true) final List<String>? subtasks,
      this.parentTaskId,
      this.isMilestone = false,
      this.isCritical = false,
      this.createdAt,
      this.updatedAt})
      : _dependencies = dependencies,
        _subtasks = subtasks;
  factory _PlanningTask.fromJson(Map<String, dynamic> json) =>
      _$PlanningTaskFromJson(json);

  @override
  final String id;
  @override
  final String taskName;
  @override
  final String planningId;
  @override
  final String? description;
  @override
  final String? assignedTo;
  @override
  final String? colorIndex;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey()
  final int progressPercentage;
  @override
  @JsonKey()
  final TaskStatus status;
  final List<String>? _dependencies;
  @override
  @JsonKey(ignore: true)
  List<String>? get dependencies {
    final value = _dependencies;
    if (value == null) return null;
    if (_dependencies is EqualUnmodifiableListView) return _dependencies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _subtasks;
  @override
  @JsonKey(ignore: true)
  List<String>? get subtasks {
    final value = _subtasks;
    if (value == null) return null;
    if (_subtasks is EqualUnmodifiableListView) return _subtasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? parentTaskId;
  @override
  @JsonKey()
  final bool isMilestone;
  @override
  @JsonKey()
  final bool isCritical;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PlanningTaskCopyWith<_PlanningTask> get copyWith =>
      __$PlanningTaskCopyWithImpl<_PlanningTask>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PlanningTaskToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PlanningTask &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.taskName, taskName) ||
                other.taskName == taskName) &&
            (identical(other.planningId, planningId) ||
                other.planningId == planningId) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.assignedTo, assignedTo) ||
                other.assignedTo == assignedTo) &&
            (identical(other.colorIndex, colorIndex) ||
                other.colorIndex == colorIndex) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality()
                .equals(other._dependencies, _dependencies) &&
            const DeepCollectionEquality().equals(other._subtasks, _subtasks) &&
            (identical(other.parentTaskId, parentTaskId) ||
                other.parentTaskId == parentTaskId) &&
            (identical(other.isMilestone, isMilestone) ||
                other.isMilestone == isMilestone) &&
            (identical(other.isCritical, isCritical) ||
                other.isCritical == isCritical) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      taskName,
      planningId,
      description,
      assignedTo,
      colorIndex,
      startDate,
      endDate,
      progressPercentage,
      status,
      const DeepCollectionEquality().hash(_dependencies),
      const DeepCollectionEquality().hash(_subtasks),
      parentTaskId,
      isMilestone,
      isCritical,
      createdAt,
      updatedAt);

  @override
  String toString() {
    return 'PlanningTask(id: $id, taskName: $taskName, planningId: $planningId, description: $description, assignedTo: $assignedTo, colorIndex: $colorIndex, startDate: $startDate, endDate: $endDate, progressPercentage: $progressPercentage, status: $status, dependencies: $dependencies, subtasks: $subtasks, parentTaskId: $parentTaskId, isMilestone: $isMilestone, isCritical: $isCritical, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$PlanningTaskCopyWith<$Res>
    implements $PlanningTaskCopyWith<$Res> {
  factory _$PlanningTaskCopyWith(
          _PlanningTask value, $Res Function(_PlanningTask) _then) =
      __$PlanningTaskCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String taskName,
      String planningId,
      String? description,
      String? assignedTo,
      String? colorIndex,
      DateTime? startDate,
      DateTime? endDate,
      int progressPercentage,
      TaskStatus status,
      @JsonKey(ignore: true) List<String>? dependencies,
      @JsonKey(ignore: true) List<String>? subtasks,
      String? parentTaskId,
      bool isMilestone,
      bool isCritical,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$PlanningTaskCopyWithImpl<$Res>
    implements _$PlanningTaskCopyWith<$Res> {
  __$PlanningTaskCopyWithImpl(this._self, this._then);

  final _PlanningTask _self;
  final $Res Function(_PlanningTask) _then;

  /// Create a copy of PlanningTask
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? taskName = null,
    Object? planningId = null,
    Object? description = freezed,
    Object? assignedTo = freezed,
    Object? colorIndex = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? progressPercentage = null,
    Object? status = null,
    Object? dependencies = freezed,
    Object? subtasks = freezed,
    Object? parentTaskId = freezed,
    Object? isMilestone = null,
    Object? isCritical = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_PlanningTask(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      taskName: null == taskName
          ? _self.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String,
      planningId: null == planningId
          ? _self.planningId
          : planningId // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      assignedTo: freezed == assignedTo
          ? _self.assignedTo
          : assignedTo // ignore: cast_nullable_to_non_nullable
              as String?,
      colorIndex: freezed == colorIndex
          ? _self.colorIndex
          : colorIndex // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _self.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus,
      dependencies: freezed == dependencies
          ? _self._dependencies
          : dependencies // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subtasks: freezed == subtasks
          ? _self._subtasks
          : subtasks // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentTaskId: freezed == parentTaskId
          ? _self.parentTaskId
          : parentTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      isMilestone: null == isMilestone
          ? _self.isMilestone
          : isMilestone // ignore: cast_nullable_to_non_nullable
              as bool,
      isCritical: null == isCritical
          ? _self.isCritical
          : isCritical // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
