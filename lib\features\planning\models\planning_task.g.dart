// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'planning_task.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlanningTaskImpl _$$PlanningTaskImplFromJson(Map<String, dynamic> json) =>
    _$PlanningTaskImpl(
      id: json['id'] as String,
      taskName: json['taskName'] as String,
      planningId: json['planningId'] as String,
      description: json['description'] as String?,
      assignedTo: json['assignedTo'] as String?,
      colorIndex: json['colorIndex'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      progressPercentage: (json['progressPercentage'] as num?)?.toInt() ?? 0,
      status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']) ??
          TaskStatus.ToDo,
      parentTaskId: json['parentTaskId'] as String?,
      isMilestone: json['isMilestone'] as bool? ?? false,
      isCritical: json['isCritical'] as bool? ?? false,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$PlanningTaskImplToJson(_$PlanningTaskImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'taskName': instance.taskName,
      'planningId': instance.planningId,
      'description': instance.description,
      'assignedTo': instance.assignedTo,
      'colorIndex': instance.colorIndex,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'progressPercentage': instance.progressPercentage,
      'status': _$TaskStatusEnumMap[instance.status]!,
      'parentTaskId': instance.parentTaskId,
      'isMilestone': instance.isMilestone,
      'isCritical': instance.isCritical,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$TaskStatusEnumMap = {
  TaskStatus.ToDo: 0,
  TaskStatus.InProgress: 1,
  TaskStatus.Done: 2,
  TaskStatus.Blocked: 3,
  TaskStatus.Esquisse: 'Esquisse',
  TaskStatus.AvantProjetSommaire: 'AvantProjetSommaire',
  TaskStatus.AvantProjetDefinitif: 'AvantProjetDefinitif',
  TaskStatus.DossierConsultation: 'DossierConsultation',
  TaskStatus.AnalyseOffres: 'AnalyseOffres',
  TaskStatus.SuiviChantier: 'SuiviChantier',
  TaskStatus.Reception: 'Reception',
};
