import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/features/planning/models/planning_task.dart';
import 'package:seqqo/features/planning/services/planning_storage_service.dart';
import 'package:seqqo/features/planning/services/planning_migration_service.dart';
import 'package:seqqo/features/planning/widgets/hierarchical_task_list.dart';
import 'package:seqqo/features/planning/widgets/notion_style_visual_planning_view.dart';
import 'package:seqqo/features/planning/widgets/time_scale_selector.dart';
import 'package:seqqo/features/planning/screens/task_detail_screen.dart';
import 'package:seqqo/features/company_directory/services/company_repository.dart';
import 'package:seqqo/features/company_directory/models/company_model.dart';
import 'package:seqqo/features/planning/models/pdf_export_options.dart';
import 'package:seqqo/features/planning/widgets/pdf_export_options_dialog.dart';
import 'package:seqqo/features/projets/services/project_storage_service.dart';
import 'package:seqqo/features/projets/models/project_data.dart';
import 'package:seqqo/features/organizations/repositories/organization_repository.dart';
import 'package:seqqo/core/services/logging_service.dart';

class EnhancedPlanningScreen extends ConsumerStatefulWidget {
  final String planningId;
  final String projectId;
  final String? planningName;
  final bool showHeader;
  final VoidCallback? onClosed;
  final bool isEmbedded;
  final bool autoOpenTaskForm;
  final bool todayButtonPressed;
  final TimeScale? timeScale;
  final bool isCondensedMode;
  final String searchQuery;
  final Function(VoidCallback)? onExportPdfCallback;

  const EnhancedPlanningScreen({
    super.key,
    required this.planningId,
    required this.projectId,
    this.planningName,
    this.showHeader = true,
    this.onClosed,
    this.isEmbedded = false,
    this.autoOpenTaskForm = false,
    this.todayButtonPressed = false,
    this.timeScale,
    this.isCondensedMode = false,
    this.searchQuery = '',
    this.onExportPdfCallback,
  });

  @override
  ConsumerState<EnhancedPlanningScreen> createState() =>
      _EnhancedPlanningScreenState();
}

class _EnhancedPlanningScreenState extends ConsumerState<EnhancedPlanningScreen>
    with SingleTickerProviderStateMixin {
  late PlanningStorageService _storageService;
  final PlanningMigrationService _migrationService =
      PlanningMigrationService(firestore: FirebaseFirestore.instance);
  final ProjectStorageService _projectService = ProjectStorageService();
  final _logger = LoggingService.getLogger('EnhancedPlanningScreen');
  late TabController _tabController;
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  List<PlanningTask> _tasks = [];
  bool _isLoading = true;
  String? _error;

  // Variables pour la barre d'outils unifiée
  bool _todayButtonPressed = false;
  bool _isCondensedMode = false;
  TimeScale _currentTimeScale = TimeScale.month;
  String _searchQuery = '';

  // Filtres
  TaskStatus? _statusFilter;
  String? _assignedToFilter;
  bool _showMilestonesOnly = false;
  bool _highlightCriticalPath = false;
  bool _showDependencies = true;

  List<Company> _companies = [];

  @override
  void initState() {
    super.initState();
    // Initialiser le service de stockage avec les paramètres requis
    _storageService = PlanningStorageService(
      firestore: FirebaseFirestore.instance,
      planningId: widget.planningId,
    );

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    _initializeNotifications();
    _checkAndMigratePlanning();
    _loadCompanies();

    // Initialiser les variables d'état avec les valeurs transmises
    _todayButtonPressed = widget.todayButtonPressed;
    _isCondensedMode = widget.isCondensedMode;
    _currentTimeScale = widget.timeScale ?? TimeScale.month;
    _searchQuery = widget.searchQuery;

    // Ouvrir automatiquement le formulaire de tâche si demandé
    if (widget.autoOpenTaskForm) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showTaskFormModal();
      });
    }

    // Exposer la callback d'export PDF si demandé
    if (widget.onExportPdfCallback != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onExportPdfCallback!(_exportToPdf);
      });
    }
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Forcer la mise à jour de l'interface pour refléter le changement d'onglet
      });
    }
  }

  /// Vérifie si le planning doit être migré vers l'organisation actuelle
  Future<void> _checkAndMigratePlanning() async {
    try {
      _logger.info(
          'Vérification de la migration du planning ${widget.planningId}');

      // Vérifier d'abord si le planningId est valide
      if (widget.planningId.isEmpty) {
        _logger.warning('Planning ID vide, impossible de charger les tâches');
        setState(() {
          _error = 'ID de planning invalide';
          _isLoading = false;
        });
        return;
      }

      // Désactiver temporairement la migration pour diagnostiquer
      // await _migrationService.checkAndMigratePlanning(
      //     widget.planningId, widget.projectId);
      _logger.info('Migration désactivée temporairement pour diagnostic');

      _loadTasks();
    } catch (e) {
      _logger
          .warning('Erreur lors de la vérification/migration du planning: $e');
      // Charger les tâches même en cas d'erreur
      _loadTasks();
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Réinitialiser le bouton "Aujourd'hui" s'il a été pressé
    if (_todayButtonPressed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _todayButtonPressed = false;
        });
      });
    }
  }

  @override
  void didUpdateWidget(EnhancedPlanningScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Réinitialiser le service et recharger les tâches si l'ID du planning a changé
    if (oldWidget.planningId != widget.planningId) {
      // Réinitialiser le service avec le nouvel ID de planning
      _storageService = PlanningStorageService(
        firestore: FirebaseFirestore.instance,
        planningId: widget.planningId,
      );
      _loadTasks();
    }

    // Mettre à jour l'état du bouton "Aujourd'hui"
    if (widget.todayButtonPressed && !oldWidget.todayButtonPressed) {
      setState(() {
        _todayButtonPressed = true;
      });
    }

    // Mettre à jour l'échelle temporelle
    if (widget.timeScale != null && widget.timeScale != _currentTimeScale) {
      setState(() {
        _currentTimeScale = widget.timeScale!;
      });
    }

    // Mettre à jour le mode condensé
    if (widget.isCondensedMode != oldWidget.isCondensedMode) {
      setState(() {
        _isCondensedMode = widget.isCondensedMode;
      });
    }

    // Mettre à jour la requête de recherche
    if (widget.searchQuery != oldWidget.searchQuery) {
      setState(() {
        _searchQuery = widget.searchQuery;
      });
    }
  }

  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
    );
    await _notificationsPlugin.initialize(initializationSettings);
  }

  Future<void> _checkAndNotifyTasks() async {
    final now = DateTime.now();
    for (final task in _tasks) {
      if (task.status != TaskStatus.Done) {
        // Tâche à venir (dans 2 jours)
        if (task.startDate != null &&
            task.startDate!.isAfter(now) &&
            task.startDate!.difference(now).inDays <= 2) {
          await _showNotification(
            'Tâche à venir',
            'La tâche "${task.taskName}" commence bientôt.',
          );
        }
        // Tâche en retard
        if (task.endDate != null && task.endDate!.isBefore(now)) {
          await _showNotification(
            'Tâche en retard',
            'La tâche "${task.taskName}" est en retard.',
          );
        }
        // Tâche critique (exemple simplifié)
        if (_isCriticalTask(task)) {
          await _showNotification(
            'Tâche critique',
            'La tâche "${task.taskName}" est sur le chemin critique.',
          );
        }
      }
    }
  }

  bool _isCriticalTask(PlanningTask task) {
    // Calcul local de _viewEndDate
    DateTime viewEndDate;
    final tasksWithEnd = _tasks.where((t) => t.endDate != null).toList();
    if (tasksWithEnd.isNotEmpty) {
      viewEndDate = tasksWithEnd
          .map((t) => t.endDate!)
          .reduce((a, b) => a.isAfter(b) ? a : b)
          .add(const Duration(days: 3));
    } else {
      viewEndDate = DateTime.now().add(const Duration(days: 30));
    }
    // Simplifié : sur le chemin critique si dépendances et proche de la fin
    if (task.dependencies != null &&
        task.dependencies!.isNotEmpty &&
        task.endDate != null) {
      return viewEndDate.difference(task.endDate!).inDays < 14;
    }
    return false;
  }

  Future<void> _showNotification(String title, String body) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails('planning_channel', 'Planning',
            channelDescription: 'Notifications du planning',
            importance: Importance.max,
            priority: Priority.high,
            showWhen: false);
    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);
    await _notificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch % 100000,
      title,
      body,
      platformChannelSpecifics,
    );
  }

  Future<void> _loadTasks() async {
    _logger.info(
        'Début du chargement des tâches pour le planning ${widget.planningId}');
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _logger.info('Appel de getTasks() sur le service de stockage');

      // Vérifier d'abord si le planning existe, sinon le créer
      await _ensurePlanningExists();

      // Utiliser la méthode getTasks() qui existe dans le service
      final tasks = await _storageService.getTasks();
      _logger.info('${tasks.length} tâches récupérées avec succès');

      setState(() {
        _tasks = tasks;
        _isLoading = false;
      });
      await _checkAndNotifyTasks();
      _logger.info('Chargement des tâches terminé avec succès');
    } catch (e) {
      _logger.severe('Erreur lors du chargement des tâches: $e');
      setState(() {
        _error = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// S'assure que le planning existe, sinon le crée
  Future<void> _ensurePlanningExists() async {
    try {
      _logger.info(
          'Vérification de l\'existence du planning ${widget.planningId}');

      // Essayer de récupérer le projet pour obtenir son nom
      final project = await _projectService.loadProject(widget.projectId);
      final projectName = project?.projectName ?? 'Projet sans nom';

      // Pour l'instant, on suppose que le planning existe
      // Dans une version future, on pourrait vérifier son existence et le créer si nécessaire
      _logger.info(
          'Planning ${widget.planningId} considéré comme existant pour le projet: $projectName');
    } catch (e) {
      _logger.warning('Erreur lors de la vérification du planning: $e');
      // Ne pas faire échouer le chargement pour cette erreur
    }
  }

  Future<void> _loadCompanies() async {
    final repo = ref.read(companyRepositoryProvider);
    repo.getCompaniesStream().first.then((companies) {
      setState(() {
        _companies = companies;
      });
    });
  }

  List<PlanningTask> get _filteredTasks {
    return _tasks.where((task) {
      // Filtre par statut
      if (_statusFilter != null && task.status != _statusFilter) {
        return false;
      }

      // Filtre par assignation
      if (_assignedToFilter != null &&
          _assignedToFilter!.isNotEmpty &&
          task.assignedTo != _assignedToFilter) {
        return false;
      }

      // Filtre par jalons
      if (_showMilestonesOnly && !task.isMilestone) {
        return false;
      }

      // Filtre par recherche
      if (_searchQuery.isNotEmpty) {
        final taskName = task.taskName.toLowerCase();
        final description = task.description?.toLowerCase() ?? '';
        final companyName =
            task.assignedTo != null && task.assignedTo!.isNotEmpty
                ? _companies
                    .firstWhere(
                      (c) => c.id == task.assignedTo,
                      orElse: () => Company(
                        id: '',
                        name: '',
                        createdAt: DateTime(2000),
                        updatedAt: DateTime(2000),
                      ),
                    )
                    .name
                    .toLowerCase()
                : '';

        return taskName.contains(_searchQuery) ||
            description.contains(_searchQuery) ||
            companyName.contains(_searchQuery);
      }

      return true;
    }).toList();
  }

  void _navigateToTaskDetail(PlanningTask task) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor:
          Colors.transparent, // Pour permettre le centrage et coins arrondis
      builder: (context) => Center(
        child: FractionallySizedBox(
          widthFactor: 0.95, // Largeur max (ajustable)
          heightFactor: 0.85, // Hauteur réduite à 85% de l'écran
          child: Material(
            color: Theme.of(context).dialogTheme.backgroundColor,
            borderRadius: BorderRadius.circular(28),
            clipBehavior: Clip.antiAlias,
            child: TaskDetailScreen(
              task: task,
              onTaskUpdated: (updatedTask) {
                setState(() {
                  final index =
                      _tasks.indexWhere((t) => t.id == updatedTask.id);
                  if (index != -1) {
                    _tasks[index] = updatedTask;
                  }
                });
                Navigator.of(context).pop();
              },
            ),
          ),
        ),
      ),
    );
  }

  void _showTaskFormModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Center(
        child: FractionallySizedBox(
          widthFactor: 0.95,
          heightFactor: 0.85, // Hauteur réduite à 85% de l'écran
          child: Material(
            color: Theme.of(context).dialogTheme.backgroundColor,
            borderRadius: BorderRadius.circular(28),
            clipBehavior: Clip.antiAlias,
            child: TaskDetailScreen(
              task: PlanningTask(
                id: '',
                planningId: widget.planningId,
                taskName: '',
                description: '',
                status: TaskStatus.ToDo,
                progressPercentage: 0,
                isMilestone: false,
                dependencies: [],
                assignedTo: '',
              ),
              onTaskUpdated: (newTask) {
                setState(() {
                  _tasks.add(newTask);
                });
                Navigator.of(context).pop();
              },
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Filtrer les tâches'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Statut',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('Tous'),
                          selected: _statusFilter == null,
                          onSelected: (selected) {
                            setDialogState(() {
                              _statusFilter = null;
                            });
                          },
                        ),
                        ...TaskStatus.values.map((status) {
                          return FilterChip(
                            label: Text(_getStatusText(status)),
                            selected: _statusFilter == status,
                            onSelected: (selected) {
                              setDialogState(() {
                                _statusFilter = selected ? status : null;
                              });
                            },
                          );
                        }),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Type de tâche',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Jalons uniquement'),
                      value: _showMilestonesOnly,
                      onChanged: (value) {
                        setDialogState(() {
                          _showMilestonesOnly = value;
                        });
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Options d\'affichage',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Afficher les dépendances'),
                      value: _showDependencies,
                      onChanged: (value) {
                        setDialogState(() {
                          _showDependencies = value;
                        });
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    SwitchListTile(
                      title:
                          const Text('Mettre en évidence le chemin critique'),
                      value: _highlightCriticalPath,
                      onChanged: (value) {
                        setDialogState(() {
                          _highlightCriticalPath = value;
                        });
                      },
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // Les filtres sont déjà mis à jour dans le StatefulBuilder
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Appliquer'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  List<String> _getPlanningConflicts() {
    final List<String> conflicts = [];
    for (final task in _tasks) {
      // Conflit : chevauchement de tâches pour la même entreprise
      if (task.assignedTo != null && task.assignedTo!.isNotEmpty) {
        final overlapping = _tasks.where((t) =>
            t.id != task.id &&
            t.assignedTo == task.assignedTo &&
            t.startDate != null &&
            t.endDate != null &&
            task.startDate != null &&
            task.endDate != null &&
            t.startDate!.isBefore(task.endDate!) &&
            t.endDate!.isAfter(task.startDate!));
        if (overlapping.isNotEmpty) {
          conflicts.add(
              'Conflit : "${task.taskName}" chevauche une autre tâche pour la même entreprise.');
        }
      }
      // Dépendance non respectée
      if (task.dependencies != null) {
        for (final dep in task.dependencies!) {
          final depTask = _tasks.firstWhere(
            (t) => t.id == dep,
            orElse: () => PlanningTask(
              id: '',
              taskName: '',
              planningId: '',
            ),
          );
          if (depTask.id.isNotEmpty &&
              depTask.endDate != null &&
              task.startDate != null &&
              task.startDate!.isBefore(depTask.endDate!)) {
            conflicts.add(
                'Dépendance non respectée : "${task.taskName}" commence avant la fin de "${depTask.taskName}".');
          }
        }
      }
    }
    return conflicts;
  }

  Widget _buildConflictBanner() {
    final conflicts = _getPlanningConflicts();
    if (conflicts.isEmpty) return const SizedBox.shrink();
    return MaterialBanner(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: conflicts.map((c) => Text(c)).toList(),
      ),
      backgroundColor: Colors.red.shade50,
      actions: [
        TextButton(
          onPressed: () {},
          child: const Text('OK'),
        ),
      ],
    );
  }

  void _showFieldProgressDialog(PlanningTask task) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Avancement terrain - ${task.taskName}'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Commentaire ou avancement terrain',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(
                        'Avancement terrain enregistré pour "${task.taskName}"')),
              );
            },
            child: const Text('Enregistrer'),
          ),
        ],
      ),
    );
  }

  /// Gère le changement de dates d'une tâche dans le diagramme de Gantt
  void _handleTaskDatesChanged(
      PlanningTask task, DateTime newStart, DateTime newEnd) async {
    try {
      // Mettre à jour la tâche localement
      final updatedTask = PlanningTask(
        id: task.id,
        planningId: task.planningId,
        taskName: task.taskName,
        description: task.description,
        status: task.status,
        startDate: newStart,
        endDate: newEnd,
        progressPercentage: task.progressPercentage,
        isMilestone: task.isMilestone,
        dependencies: task.dependencies,
        assignedTo: task.assignedTo,
        parentTaskId: task.parentTaskId,
      );

      // Mettre à jour l'état local
      setState(() {
        final index = _tasks.indexWhere((t) => t.id == task.id);
        if (index != -1) {
          _tasks[index] = updatedTask;
        }
      });

      // Enregistrer les modifications dans Firestore
      await _storageService.updateTask(updatedTask);

      // Afficher une confirmation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Dates de "${task.taskName}" mises à jour'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Gérer l'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour des dates: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Navigue vers l'écran de gestion des versions de référence
  void _navigateToBaselineManagement() {
    // Afficher un message indiquant que cette fonctionnalité est en cours de développement
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'La gestion des versions de référence sera disponible prochainement'),
      ),
    );
  }

  /// Charge les données du projet depuis Firestore
  Future<ProjectData?> _loadProjectData() async {
    try {
      _logger.info('Chargement des données du projet: ${widget.projectId}');
      final projectData = await _projectService.loadProject(widget.projectId);

      if (projectData == null) {
        _logger
            .severe('Erreur lors du chargement du projet: Projet non trouvé');
        return null;
      }

      return projectData;
    } catch (e) {
      _logger.severe('Erreur lors du chargement du projet: $e');
      return null;
    }
  }

  /// Exporte le planning en PDF
  Future<void> _exportToPdf() async {
    try {
      // Récupérer les données du projet
      final projectData = await _loadProjectData();
      if (projectData == null) {
        throw Exception('Projet non trouvé');
      }

      // Créer une map des noms d'entreprises
      final companyNames = <String, String>{};
      for (final company in _companies) {
        companyNames[company.id] = company.name;
      }

      // Récupérer l'ID de l'organisation actuelle
      final prefs = await SharedPreferences.getInstance();
      final organizationId = prefs.getString('current_organization_id');

      // Récupérer les données de l'organisation si disponible
      String? organizationLogoUrl;
      String? organizationPrimaryColor;
      String? organizationName;

      if (organizationId != null && organizationId.isNotEmpty) {
        try {
          final organizationRepository =
              OrganizationRepository(FirebaseFirestore.instance);
          final organization =
              await organizationRepository.getOrganization(organizationId);

          if (organization != null) {
            organizationLogoUrl = organization.logoUrl;
            organizationName = organization.name;

            // Récupérer la couleur primaire depuis les settings
            final settings = organization.settings ?? {};
            organizationPrimaryColor = settings['primaryColor'] as String?;
          }
        } catch (e) {
          _logger.warning(
              'Erreur lors de la récupération des données de l\'organisation: $e');
        }
      }

      // Options d'export par défaut
      final defaultOptions = PlanningPdfExportOptions();

      // Vérifier si le widget est toujours monté avant d'afficher le dialogue
      if (!mounted) return;

      // Afficher le dialogue d'options d'export avec aperçu
      final PlanningPdfExportOptions? exportOptions =
          await showDialog<PlanningPdfExportOptions>(
        context: context,
        builder: (context) => PdfExportOptionsDialog(
          initialOptions: defaultOptions,
          tasks: _filteredTasks,
          projectData: projectData,
          companyNames: companyNames,
          organizationId: organizationId,
          organizationLogoUrl: organizationLogoUrl,
          organizationPrimaryColor: organizationPrimaryColor,
          organizationName: organizationName,
        ),
      );

      // Si l'utilisateur a annulé, ne rien faire
      if (exportOptions == null) {
        return;
      }

      // Le PDF est maintenant géré directement par le dialogue avec aperçu
      // Aucune action supplémentaire nécessaire ici
    } catch (e) {
      _logger.severe('Erreur lors de la préparation de l\'export PDF: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la préparation de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Suppression de l'AppBar pour s'intégrer avec l'AppBar verte au-dessus
      appBar: null,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text('Erreur: $_error'))
              : _tasks.isEmpty
                  ? _buildEmptyState()
                  : Row(
                      children: [
                        // NavigationRail avec style Notion
                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            border: Border(
                              right: BorderSide(
                                color: Theme.of(context)
                                    .dividerColor
                                    .withAlpha(40),
                                width: 1,
                              ),
                            ),
                          ),
                          child: NavigationRail(
                            selectedIndex: _tabController.index,
                            onDestinationSelected: (int index) {
                              setState(() {
                                _tabController.animateTo(index);
                              });
                            },
                            labelType: NavigationRailLabelType.all,
                            backgroundColor: Colors.transparent,
                            useIndicator: false,
                            minWidth: 80,
                            minExtendedWidth: 120,
                            destinations: [
                              // Destination Liste avec effet de survol
                              _buildHoverableDestination(
                                context,
                                isSelected: _tabController.index == 0,
                                icon: Icons.list,
                                label: 'Liste',
                              ),

                              // Destination Gantt avec effet de survol
                              _buildHoverableDestination(
                                context,
                                isSelected: _tabController.index == 1,
                                icon: Icons.timeline,
                                label: 'Gantt',
                              ),
                            ],
                            selectedIconTheme: IconThemeData(
                              color: Theme.of(context).colorScheme.primary,
                              size: 24,
                            ),
                            unselectedIconTheme: IconThemeData(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              size: 24,
                            ),
                            selectedLabelTextStyle: TextStyle(
                              fontFamily: 'RedditSans',
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                            unselectedLabelTextStyle: TextStyle(
                              fontFamily: 'RedditSans',
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),

                        // Contenu principal
                        Expanded(
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              _buildConflictBanner(),
                              Expanded(
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  transitionBuilder: (Widget child,
                                      Animation<double> animation) {
                                    return FadeTransition(
                                      opacity: animation,
                                      child: child,
                                    );
                                  },
                                  child: _tabController.index == 0
                                      ? Column(
                                          key: const ValueKey<String>(
                                              'list_view'),
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            // Filtres en haut
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 16,
                                                  right: 16,
                                                  top: 4,
                                                  bottom: 4),
                                              child: _buildFilterChips(),
                                            ),
                                            // Liste des tâches qui prend tout l'espace disponible
                                            Expanded(
                                              child: HierarchicalTaskList(
                                                tasks: _filteredTasks,
                                                onTaskTap:
                                                    _navigateToTaskDetail,
                                                showDependencies:
                                                    _showDependencies,
                                                companies: _companies,
                                                onFieldProgress:
                                                    _showFieldProgressDialog,
                                              ),
                                            ),
                                          ],
                                        )
                                      : NotionStyleVisualPlanningView(
                                          key: const ValueKey<String>(
                                              'gantt_view'),
                                          tasks: _filteredTasks,
                                          onTaskTap: _navigateToTaskDetail,
                                          showDependencies: _showDependencies,
                                          highlightCriticalPath:
                                              _highlightCriticalPath,
                                          companies: _companies,
                                          onTaskDatesChanged:
                                              _handleTaskDatesChanged,
                                          compact: widget.isEmbedded ||
                                              _isCondensedMode,
                                          showFilters: true,
                                          showProgressIndicator: true,
                                          onExportPdf: _exportToPdf,
                                          onManageVersions:
                                              _navigateToBaselineManagement,
                                          todayButtonPressed:
                                              _todayButtonPressed,
                                          timeScale: _currentTimeScale,
                                          searchQuery: _searchQuery,
                                          onShowFilters: widget.isEmbedded
                                              ? _showFilterDialog
                                              : null,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
      // Suppression du bouton flottant car nous avons déjà un bouton d'ajout dans la barre d'actions
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'Aucune tâche trouvée',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Commencez par ajouter des tâches à votre planning.',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Ajouter une tâche'),
            onPressed: _showTaskFormModal,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        if (_statusFilter != null)
          Chip(
            label: Text('Statut: ${_getStatusText(_statusFilter!)}'),
            deleteIcon: const Icon(Icons.close, size: 18),
            onDeleted: () {
              setState(() {
                _statusFilter = null;
              });
            },
          ),
        if (_assignedToFilter != null && _assignedToFilter!.isNotEmpty)
          Chip(
            label: Text('Assigné à: $_assignedToFilter'),
            deleteIcon: const Icon(Icons.close, size: 18),
            onDeleted: () {
              setState(() {
                _assignedToFilter = null;
              });
            },
          ),
        if (_showMilestonesOnly)
          Chip(
            label: const Text('Jalons uniquement'),
            deleteIcon: const Icon(Icons.close, size: 18),
            onDeleted: () {
              setState(() {
                _showMilestonesOnly = false;
              });
            },
          ),
      ],
    );
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.ToDo:
        return 'À faire';
      case TaskStatus.InProgress:
        return 'En cours';
      case TaskStatus.Done:
        return 'Terminé';
      case TaskStatus.Blocked:
        return 'Bloqué';
      case TaskStatus.Esquisse:
        return 'Esquisse';
      default:
        return status.toString().split('.').last;
    }
  }

  // Méthode pour créer une destination NavigationRail avec effet de survol
  NavigationRailDestination _buildHoverableDestination(
    BuildContext context, {
    required bool isSelected,
    required IconData icon,
    required String label,
  }) {
    return NavigationRailDestination(
      icon: _HoverableNavigationContainer(
        isSelected: isSelected,
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerLowest,
        child: _HoverableNavigationIcon(
          icon: icon,
          isSelected: isSelected,
          primaryColor: Theme.of(context).colorScheme.primary,
          unselectedColor: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
      label: _HoverableNavigationLabel(
        label: label,
        isSelected: isSelected,
        primaryColor: Theme.of(context).colorScheme.primary,
        unselectedColor: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      padding: const EdgeInsets.symmetric(vertical: 8),
    );
  }
}

// Widget personnalisé pour ajouter un effet de survol aux icônes de navigation
class _HoverableNavigationIcon extends StatefulWidget {
  final IconData icon;
  final bool isSelected;
  final Color primaryColor;
  final Color unselectedColor;

  const _HoverableNavigationIcon({
    required this.icon,
    required this.isSelected,
    required this.primaryColor,
    required this.unselectedColor,
  });

  @override
  State<_HoverableNavigationIcon> createState() =>
      _HoverableNavigationIconState();
}

class _HoverableNavigationIconState extends State<_HoverableNavigationIcon>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _updateColorAnimation();
  }

  @override
  void didUpdateWidget(_HoverableNavigationIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isSelected != widget.isSelected ||
        oldWidget.primaryColor != widget.primaryColor ||
        oldWidget.unselectedColor != widget.unselectedColor) {
      _updateColorAnimation();
    }
  }

  void _updateColorAnimation() {
    _colorAnimation = ColorTween(
      begin: widget.isSelected ? widget.primaryColor : widget.unselectedColor,
      end: widget.primaryColor,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
          _controller.forward();
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
          _controller.reverse();
        });
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isSelected ? 1.0 : _scaleAnimation.value,
            child: Icon(
              widget.icon,
              color: widget.isSelected
                  ? widget.primaryColor
                  : _isHovered
                      ? _colorAnimation.value
                      : widget.unselectedColor,
              size: 24,
            ),
          );
        },
      ),
    );
  }
}

// Widget personnalisé pour ajouter un effet de survol aux étiquettes de navigation
class _HoverableNavigationLabel extends StatefulWidget {
  final String label;
  final bool isSelected;
  final Color primaryColor;
  final Color unselectedColor;

  const _HoverableNavigationLabel({
    required this.label,
    required this.isSelected,
    required this.primaryColor,
    required this.unselectedColor,
  });

  @override
  State<_HoverableNavigationLabel> createState() =>
      _HoverableNavigationLabelState();
}

class _HoverableNavigationLabelState extends State<_HoverableNavigationLabel>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _fontWeightAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _updateAnimations();
  }

  @override
  void didUpdateWidget(_HoverableNavigationLabel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isSelected != widget.isSelected ||
        oldWidget.primaryColor != widget.primaryColor ||
        oldWidget.unselectedColor != widget.unselectedColor) {
      _updateAnimations();
    }
  }

  void _updateAnimations() {
    _colorAnimation = ColorTween(
      begin: widget.isSelected ? widget.primaryColor : widget.unselectedColor,
      end: widget.primaryColor,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _fontWeightAnimation = Tween<double>(
      begin: widget.isSelected ? 1.0 : 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
          _controller.forward();
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
          _controller.reverse();
        });
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          // Interpoler entre w400 et w500 pour l'effet de poids de police
          final fontWeight = widget.isSelected
              ? FontWeight.w500
              : _isHovered
                  ? FontWeight.lerp(
                      FontWeight.w400,
                      FontWeight.w500,
                      _fontWeightAnimation.value,
                    )
                  : FontWeight.w400;

          return Text(
            widget.label,
            style: TextStyle(
              fontFamily: 'RedditSans',
              fontSize: 14,
              fontWeight: fontWeight,
              color: widget.isSelected
                  ? widget.primaryColor
                  : _isHovered
                      ? _colorAnimation.value
                      : widget.unselectedColor,
              letterSpacing: -0.25,
            ),
          );
        },
      ),
    );
  }
}

// Widget personnalisé pour ajouter un effet de fond au survol
class _HoverableNavigationContainer extends StatefulWidget {
  final Widget child;
  final bool isSelected;
  final Color backgroundColor;

  const _HoverableNavigationContainer({
    required this.child,
    required this.isSelected,
    required this.backgroundColor,
  });

  @override
  State<_HoverableNavigationContainer> createState() =>
      _HoverableNavigationContainerState();
}

class _HoverableNavigationContainerState
    extends State<_HoverableNavigationContainer>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(
      begin: widget.isSelected ? 0.3 : 0.0,
      end: 0.3,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.isSelected) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(_HoverableNavigationContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isSelected != widget.isSelected) {
      if (widget.isSelected) {
        _controller.value = 1.0;
      } else if (!_isHovered) {
        _controller.value = 0.0;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
          if (!widget.isSelected) {
            _controller.forward();
          }
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
          if (!widget.isSelected) {
            _controller.reverse();
          }
        });
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: widget.backgroundColor.withAlpha(
                (widget.isSelected ? 0.3 : _opacityAnimation.value) * 255 ~/ 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: widget.child,
          );
        },
      ),
    );
  }
}
