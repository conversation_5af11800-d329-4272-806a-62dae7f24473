import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seqqo/features/planning/models/planning_task.dart';
import 'package:seqqo/features/planning/services/planning_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/features/planning/utils/task_colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seqqo/features/company_directory/services/company_repository.dart';
import 'package:seqqo/features/company_directory/models/company_model.dart';
import 'package:seqqo/features/planning/providers/task_report_items_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:seqqo/core/services/logging_service.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/core/widgets/app_text_field.dart';
import 'package:seqqo/core/widgets/date_range_picker.dart';
import 'package:seqqo/core/widgets/simple_dropdown.dart';

class TaskDetailScreen extends ConsumerStatefulWidget {
  final PlanningTask task;
  final Function(PlanningTask) onTaskUpdated;

  const TaskDetailScreen({
    super.key,
    required this.task,
    required this.onTaskUpdated,
  });

  @override
  ConsumerState<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends ConsumerState<TaskDetailScreen> {
  late final PlanningStorageService _storageService;
  final _formKey = GlobalKey<FormState>();
  final _logger = LoggingService.getLogger('TaskDetailScreen');

  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late DateTime? _startDate;
  late DateTime? _endDate;
  late TaskStatus _status;
  late int _progressPercentage;
  late String? _colorIndex;
  late bool _isMilestone;

  bool _isLoading = false;
  String? _error;
  List<PlanningTask> _availableTasks = [];
  final List<String> _selectedDependencies = [];
  List<Company> _companies = [];
  bool _companiesLoading = true;

  @override
  void initState() {
    super.initState();
    _storageService = PlanningStorageService(
      firestore: FirebaseFirestore.instance,
      planningId: widget.task.planningId,
    );
    _nameController = TextEditingController(text: widget.task.taskName);
    _descriptionController =
        TextEditingController(text: widget.task.description);
    _startDate = widget.task.startDate;
    _endDate = widget.task.endDate;
    _status = widget.task.status;
    _progressPercentage = widget.task.progressPercentage;
    _isMilestone = widget.task.isMilestone;
    _colorIndex = widget.task.colorIndex;

    // Initialiser les dépendances
    if (widget.task.dependencies != null) {
      for (var dep in widget.task.dependencies!) {
        _selectedDependencies.add(dep);
      }
    }

    _loadAvailableTasks();
    _loadCompanies();
  }

  // Variable pour suivre si l'écran est en cours de fermeture
  bool _isDisposing = false;

  @override
  void dispose() {
    _isDisposing = true;
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableTasks() async {
    try {
      // Si l'écran est en cours de fermeture, ne pas continuer
      if (_isDisposing) return;

      final tasks = await _storageService.getTasks();

      // Vérifier à nouveau si l'écran est en cours de fermeture ou a été fermé
      if (_isDisposing || !mounted) return;

      setState(() {
        // Exclure la tâche actuelle
        _availableTasks = tasks.where((t) => t.id != widget.task.id).toList();
      });
    } catch (e) {
      // Vérifier si l'écran est en cours de fermeture ou a été fermé
      if (_isDisposing || !mounted) return;

      setState(() {
        _error = 'Erreur lors du chargement des tâches: ${e.toString()}';
      });
    }
  }

  Future<void> _loadCompanies() async {
    // Si l'écran est en cours de fermeture, ne pas continuer
    if (_isDisposing) return;

    final repo = ref.read(companyRepositoryProvider);
    repo.getCompaniesStream().first.then((companies) {
      // Vérifier si l'écran est en cours de fermeture ou a été fermé
      if (_isDisposing || !mounted) return;

      setState(() {
        _companies = companies;
        _companiesLoading = false;
      });
    });
  }

  Future<void> _saveTask() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Préparer les dépendances
      List<String> dependencies = List.from(_selectedDependencies);

      // Vérifier si c'est une nouvelle tâche ou une mise à jour
      final isNewTask = widget.task.id.isEmpty;

      // Si c'est une nouvelle tâche, générer un nouvel ID
      final String taskId = isNewTask ? const Uuid().v4() : widget.task.id;

      // Créer la tâche mise à jour
      final updatedTask = widget.task.copyWith(
        id: taskId, // Utiliser le nouvel ID pour les nouvelles tâches
        taskName: _nameController.text,
        description: _descriptionController.text,
        startDate: _startDate,
        endDate: _endDate,
        status: _status,
        progressPercentage: _progressPercentage,
        isMilestone: _isMilestone,
        dependencies: dependencies,
        colorIndex: _colorIndex,
        updatedAt: DateTime.now(),
      );

      // Sauvegarder la tâche
      if (isNewTask) {
        // Utiliser addTask pour les nouvelles tâches
        await _storageService.addTask(updatedTask);
        _logger.info(
            'Nouvelle tâche créée: ${updatedTask.id} - ${updatedTask.taskName}');
      } else {
        // Utiliser updateTask pour les tâches existantes
        await _storageService.updateTask(updatedTask);
        _logger.info(
            'Tâche mise à jour: ${updatedTask.id} - ${updatedTask.taskName}');
      }

      // Notifier le parent
      widget.onTaskUpdated(updatedTask);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isNewTask
                ? 'Tâche créée avec succès'
                : 'Tâche mise à jour avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = 'Erreur lors de la sauvegarde: ${e.toString()}';
        });

        // Afficher un message d'erreur
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $_error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDependenciesDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                'Gérer les dépendances',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontFamily: 'RedditSans',
                  fontWeight: FontWeight.w600,
                ),
              ),
              backgroundColor: theme.colorScheme.surface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: _availableTasks.isEmpty
                    ? Center(
                        child: Text(
                          'Aucune autre tâche disponible',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontFamily: 'RedditSans',
                            fontStyle: FontStyle.italic,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: _availableTasks.length,
                        itemBuilder: (context, index) {
                          final task = _availableTasks[index];
                          final isSelected =
                              _selectedDependencies.contains(task.id);

                          return CheckboxListTile(
                            title: Text(
                              task.taskName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontFamily: 'RedditSans',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: task.startDate != null &&
                                    task.endDate != null
                                ? Text(
                                    '${DateFormat('dd/MM/yyyy').format(task.startDate!)} - '
                                    '${DateFormat('dd/MM/yyyy').format(task.endDate!)}',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      fontFamily: 'RedditSans',
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  )
                                : null,
                            value: isSelected,
                            activeColor: theme.colorScheme.primary,
                            checkColor: theme.colorScheme.onPrimary,
                            onChanged: (value) {
                              setDialogState(() {
                                if (value == true) {
                                  _selectedDependencies.add(task.id);
                                } else {
                                  _selectedDependencies.remove(task.id);
                                }
                              });
                            },
                          );
                        },
                      ),
              ),
              actions: [
                AppButton.ghost(
                  text: 'Annuler',
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  size: AppButtonSize.small,
                ),
                AppButton.primary(
                  text: 'Confirmer',
                  onPressed: () {
                    Navigator.pop(context);
                    setState(() {}); // Rafraîchir l'UI
                  },
                  size: AppButtonSize.small,
                ),
              ],
              actionsPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Détails de la tâche',
          style: theme.textTheme.titleLarge?.copyWith(
            fontFamily: 'RedditSans',
            fontSize: 16, // Taille standardisée pour les titres
          ),
        ),
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: Icon(
              Icons.save,
              color: theme.colorScheme.primary,
            ),
            onPressed: _isLoading ? null : _saveTask,
            tooltip: 'Enregistrer',
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                    strokeWidth: 2,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Chargement en cours...',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontFamily: 'RedditSans',
                      fontSize: 14, // Taille standardisée
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Informations de base
                    Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withAlpha(40),
                          width: 1,
                        ),
                      ),
                      color: theme.colorScheme.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Informations générales',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontFamily: 'RedditSans',
                                fontSize:
                                    15, // Taille standardisée pour les sous-titres
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _nameController,
                              label: 'Nom de la tâche',
                              hint: 'Entrez le nom de la tâche',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Veuillez entrer un nom';
                                }
                                return null;
                              },
                              isRequired: true,
                            ),
                            const SizedBox(height: 16),
                            AppTextField.multiline(
                              controller: _descriptionController,
                              label: 'Description',
                              hint:
                                  'Entrez une description détaillée de la tâche',
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),
                            _companiesLoading
                                ? Center(
                                    child: CircularProgressIndicator(
                                      color: theme.colorScheme.primary,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : SimpleDropdown<String>(
                                    value: (_companies.any((c) =>
                                            c.id == widget.task.assignedTo))
                                        ? widget.task.assignedTo
                                        : null,
                                    decoration: InputDecoration(
                                      labelText: "Entreprise assignée",
                                      labelStyle:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        fontFamily: 'RedditSans',
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: theme.colorScheme.outline,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: theme.colorScheme.outline,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: theme.colorScheme.primary,
                                          width: 1.5,
                                        ),
                                      ),
                                    ),
                                    items: _companies
                                        .map((company) =>
                                            SimpleDropdownItem<String>(
                                              value: company.id,
                                              child: Text(
                                                company.name,
                                                style: theme
                                                    .textTheme.bodyMedium
                                                    ?.copyWith(
                                                  fontFamily: 'RedditSans',
                                                ),
                                              ),
                                            ))
                                        .toList(),
                                    hint: "Sélectionner une entreprise",
                                    onChanged: (value) {
                                      setState(() {
                                        // Mettre à jour la tâche avec copyWith
                                        final updatedTask = widget.task
                                            .copyWith(assignedTo: value ?? '');
                                        // Appeler onTaskUpdated pour notifier les changements si nécessaire
                                        // widget.onTaskUpdated(updatedTask);
                                        // ou mettre à jour l'état local si la sauvegarde se fait plus tard
                                        // Pour l'instant, on suppose que la sauvegarde gère la mise à jour de l'état global
                                      });
                                    },
                                  ),
                            const SizedBox(height: 16),
                            SwitchListTile(
                              title: Text(
                                'Jalon',
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  fontFamily: 'RedditSans',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              subtitle: Text(
                                'Un jalon est un événement clé sans durée',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  fontFamily: 'RedditSans',
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              value: _isMilestone,
                              onChanged: (value) {
                                setState(() {
                                  _isMilestone = value;
                                });
                              },
                              activeColor: theme.colorScheme.primary,
                              contentPadding:
                                  const EdgeInsets.symmetric(horizontal: 0),
                            ),

                            // Sélecteur de couleur
                            const SizedBox(height: 16),
                            Text(
                              'Couleur de la tâche',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontFamily: 'RedditSans',
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Wrap(
                              spacing: 12,
                              runSpacing: 12,
                              children: List.generate(
                                TaskColors.colorCount,
                                (index) => GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _colorIndex = index.toString();
                                    });
                                  },
                                  child: Container(
                                    width: 36,
                                    height: 36,
                                    decoration: BoxDecoration(
                                      color: TaskColors.colors[index],
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: _colorIndex == index.toString()
                                            ? theme.colorScheme.primary
                                            : theme.colorScheme.outline,
                                        width: _colorIndex == index.toString()
                                            ? 2
                                            : 1,
                                      ),
                                      boxShadow: _colorIndex == index.toString()
                                          ? [
                                              BoxShadow(
                                                color: theme.colorScheme.shadow,
                                                blurRadius: 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ]
                                          : null,
                                    ),
                                    child: _colorIndex == index.toString()
                                        ? const Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 18,
                                          )
                                        : null,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _colorIndex != null
                                  ? 'Couleur sélectionnée: ${TaskColors.getColorName(int.parse(_colorIndex ?? "0"))}'
                                  : 'Aucune couleur sélectionnée',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontFamily: 'RedditSans',
                                fontStyle: FontStyle.italic,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Dates et progression
                    Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withAlpha(40),
                          width: 1,
                        ),
                      ),
                      color: theme.colorScheme.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Planification',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontFamily: 'RedditSans',
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: DatePickerField(
                                    initialDate: _startDate,
                                    label: 'Date de début',
                                    hint: 'Sélectionner une date',
                                    onDateChanged: (date) {
                                      setState(() {
                                        _startDate = date;
                                        // Si la date de fin est avant la date de début, ajuster la date de fin
                                        if (_endDate != null &&
                                            date != null &&
                                            _endDate!.isBefore(date)) {
                                          _endDate =
                                              date.add(const Duration(days: 1));
                                        }
                                      });
                                    },
                                    required: true,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: DatePickerField(
                                    initialDate: _endDate,
                                    label: 'Date de fin',
                                    hint: 'Sélectionner une date',
                                    onDateChanged: (date) {
                                      setState(() {
                                        _endDate = date;
                                        // Si la date de début est après la date de fin, ajuster la date de début
                                        if (_startDate != null &&
                                            date != null &&
                                            _startDate!.isAfter(date)) {
                                          _startDate = date.subtract(
                                              const Duration(days: 1));
                                        }
                                      });
                                    },
                                    required: true,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            SimpleDropdown<TaskStatus>(
                              decoration: InputDecoration(
                                labelText: 'Statut',
                                labelStyle:
                                    theme.textTheme.bodyMedium?.copyWith(
                                  fontFamily: 'RedditSans',
                                  color: theme.colorScheme.onSurface,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: theme.colorScheme.outline,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: theme.colorScheme.outline,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: theme.colorScheme.primary,
                                    width: 1.5,
                                  ),
                                ),
                              ),
                              value: _status,
                              items: TaskStatus.values.map((status) {
                                return SimpleDropdownItem<TaskStatus>(
                                  value: status,
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 12,
                                        height: 12,
                                        decoration: BoxDecoration(
                                          color: _getTaskStatusColor(status),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        _getStatusText(status),
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                          fontFamily: 'RedditSans',
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                              hint: "Sélectionner un statut",
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _status = value;
                                    // Mettre à jour automatiquement la progression
                                    if (value == TaskStatus.Done) {
                                      _progressPercentage = 100;
                                    } else if (value == TaskStatus.ToDo &&
                                        _progressPercentage == 100) {
                                      _progressPercentage = 0;
                                    }
                                  });
                                }
                              },
                            ),
                            const SizedBox(height: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Progression',
                                      style:
                                          theme.textTheme.titleSmall?.copyWith(
                                        fontFamily: 'RedditSans',
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color:
                                            theme.colorScheme.primaryContainer,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '$_progressPercentage%',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontFamily: 'RedditSans',
                                          fontWeight: FontWeight.w600,
                                          color: theme
                                              .colorScheme.onPrimaryContainer,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                SliderTheme(
                                  data: SliderTheme.of(context).copyWith(
                                    trackHeight: 4,
                                    thumbShape: const RoundSliderThumbShape(
                                      enabledThumbRadius: 8,
                                      elevation: 2,
                                    ),
                                    overlayShape: const RoundSliderOverlayShape(
                                      overlayRadius: 16,
                                    ),
                                    valueIndicatorShape:
                                        const PaddleSliderValueIndicatorShape(),
                                    valueIndicatorTextStyle:
                                        theme.textTheme.bodySmall?.copyWith(
                                      fontFamily: 'RedditSans',
                                      color: Colors.white,
                                    ),
                                    showValueIndicator:
                                        ShowValueIndicator.always,
                                    activeTrackColor: theme.colorScheme.primary,
                                    inactiveTrackColor: theme
                                        .colorScheme.surfaceContainerHighest,
                                    thumbColor: theme.colorScheme.primary,
                                    overlayColor:
                                        theme.colorScheme.primary.withAlpha(40),
                                  ),
                                  child: Slider(
                                    value: _progressPercentage.toDouble(),
                                    min: 0,
                                    max: 100,
                                    divisions: 20,
                                    label: '$_progressPercentage%',
                                    onChanged: (value) {
                                      setState(() {
                                        _progressPercentage = value.round();
                                        // Mettre à jour automatiquement le statut
                                        if (_progressPercentage == 100 &&
                                            _status != TaskStatus.Done) {
                                          _status = TaskStatus.Done;
                                        } else if (_progressPercentage > 0 &&
                                            _progressPercentage < 100 &&
                                            _status != TaskStatus.InProgress) {
                                          _status = TaskStatus.InProgress;
                                        } else if (_progressPercentage == 0 &&
                                            _status != TaskStatus.ToDo &&
                                            _status != TaskStatus.Blocked) {
                                          _status = TaskStatus.ToDo;
                                        }
                                      });
                                    },
                                  ),
                                ),
                                // Indicateurs de progression
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        '0%',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontFamily: 'RedditSans',
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                      Text(
                                        '50%',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontFamily: 'RedditSans',
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                      Text(
                                        '100%',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontFamily: 'RedditSans',
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Mentionné dans les CR
                    Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withAlpha(40),
                          width: 1,
                        ),
                      ),
                      color: theme.colorScheme.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Mentionné dans les CR',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontFamily: 'RedditSans',
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Utiliser le provider pour récupérer les éléments liés
                            Consumer(
                              builder: (context, ref, child) {
                                final relatedItemsAsync = ref.watch(
                                    relatedReportItemsProvider(widget.task.id));

                                return relatedItemsAsync.when(
                                  data: (items) {
                                    if (items.isEmpty) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 16),
                                        child: Center(
                                          child: Text(
                                            'Aucune mention dans les comptes rendus',
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(
                                              fontFamily: 'RedditSans',
                                              fontStyle: FontStyle.italic,
                                              color: theme
                                                  .colorScheme.onSurfaceVariant,
                                            ),
                                          ),
                                        ),
                                      );
                                    }

                                    return ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: items.length,
                                      itemBuilder: (context, index) {
                                        final item = items[index];

                                        return Card(
                                          margin:
                                              const EdgeInsets.only(bottom: 12),
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            side: BorderSide(
                                              color: theme.colorScheme.outline
                                                  .withAlpha(50),
                                              width: 1,
                                            ),
                                          ),
                                          child: ListTile(
                                            contentPadding:
                                                const EdgeInsets.all(12),
                                            title: Text(
                                              item.item.description,
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                fontFamily: 'RedditSans',
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            subtitle: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const SizedBox(height: 8),
                                                Row(
                                                  children: [
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                        vertical: 2,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: _getStatusColor(
                                                                item.item
                                                                    .status)
                                                            .withAlpha(25),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color:
                                                              _getStatusColor(
                                                                  item.item
                                                                      .status),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        _getStatusName(
                                                            item.item.status),
                                                        style: theme
                                                            .textTheme.bodySmall
                                                            ?.copyWith(
                                                          fontFamily:
                                                              'RedditSans',
                                                          color:
                                                              _getStatusColor(
                                                                  item.item
                                                                      .status),
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    if (item.item.dueDate !=
                                                        null)
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.event,
                                                            size: 14,
                                                            color: theme
                                                                .colorScheme
                                                                .onSurfaceVariant,
                                                          ),
                                                          const SizedBox(
                                                              width: 4),
                                                          Text(
                                                            'Échéance: ${DateFormat('dd/MM/yyyy').format(item.item.dueDate!)}',
                                                            style: theme
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                              fontFamily:
                                                                  'RedditSans',
                                                              color: theme
                                                                  .colorScheme
                                                                  .onSurfaceVariant,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                  ],
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .description_outlined,
                                                      size: 14,
                                                      color: theme.colorScheme
                                                          .onSurfaceVariant,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'CR n°${item.reportNumber} du ${DateFormat('dd/MM/yyyy').format(item.reportDate)}',
                                                      style: theme
                                                          .textTheme.bodySmall
                                                          ?.copyWith(
                                                        fontFamily:
                                                            'RedditSans',
                                                        color: theme.colorScheme
                                                            .onSurfaceVariant,
                                                        fontStyle:
                                                            FontStyle.italic,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            onTap: () {
                                              // Naviguer vers le rapport
                                              // À implémenter ultérieurement
                                            },
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  loading: () => Center(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      child: CircularProgressIndicator(
                                        color: theme.colorScheme.primary,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                                  error: (error, stackTrace) => Center(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      child: Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color:
                                              theme.colorScheme.errorContainer,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.error_outline,
                                              color: theme.colorScheme.error,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),
                                            Flexible(
                                              child: Text(
                                                'Erreur: $error',
                                                style: theme
                                                    .textTheme.bodyMedium
                                                    ?.copyWith(
                                                  fontFamily: 'RedditSans',
                                                  color: theme.colorScheme
                                                      .onErrorContainer,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Dépendances
                    Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withAlpha(40),
                          width: 1,
                        ),
                      ),
                      color: theme.colorScheme.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Dépendances',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontFamily: 'RedditSans',
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                AppButton.ghost(
                                  text: 'Gérer',
                                  icon: Icons.edit,
                                  onPressed: _showDependenciesDialog,
                                  size: AppButtonSize.small,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            if (_selectedDependencies.isEmpty)
                              Text(
                                'Aucune dépendance',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontFamily: 'RedditSans',
                                  fontStyle: FontStyle.italic,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              )
                            else
                              Column(
                                children: _selectedDependencies.map((taskId) {
                                  final task = _availableTasks.firstWhere(
                                    (t) => t.id == taskId,
                                    orElse: () => PlanningTask(
                                      id: '',
                                      taskName: 'Tâche inconnue',
                                      planningId: '',
                                    ),
                                  );

                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    elevation: 0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side: BorderSide(
                                        color: theme.colorScheme.outline
                                            .withAlpha(50),
                                        width: 1,
                                      ),
                                    ),
                                    child: ListTile(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 4,
                                      ),
                                      leading: Icon(
                                        Icons.arrow_forward,
                                        color: theme.colorScheme.primary,
                                        size: 20,
                                      ),
                                      title: Text(
                                        task.id.isEmpty
                                            ? 'Tâche inconnue'
                                            : task.taskName,
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                          fontFamily: 'RedditSans',
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      subtitle: task.startDate != null &&
                                              task.endDate != null
                                          ? Text(
                                              '${DateFormat('dd/MM/yyyy').format(task.startDate!)} - '
                                              '${DateFormat('dd/MM/yyyy').format(task.endDate!)}',
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                fontFamily: 'RedditSans',
                                                color: theme.colorScheme
                                                    .onSurfaceVariant,
                                              ),
                                            )
                                          : null,
                                      trailing: IconButton(
                                        icon: Icon(
                                          Icons.delete_outline,
                                          color: theme.colorScheme.error,
                                          size: 20,
                                        ),
                                        tooltip: 'Supprimer la dépendance',
                                        onPressed: () {
                                          setState(() {
                                            _selectedDependencies
                                                .remove(taskId);
                                          });
                                        },
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                          ],
                        ),
                      ),
                    ),

                    if (_error != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: theme.colorScheme.error,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _error!,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontFamily: 'RedditSans',
                                  color: theme.colorScheme.onErrorContainer,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: BottomAppBar(
        color: theme.colorScheme.surface,
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.task.id.isNotEmpty
                    ? 'ID: ${widget.task.id.substring(0, 8)}...'
                    : 'Nouvelle tâche',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontFamily: 'RedditSans',
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              AppButton.primary(
                text: 'Enregistrer',
                onPressed: _isLoading ? null : _saveTask,
                icon: Icons.save,
                size: AppButtonSize.small,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      // Statuts génériques
      case TaskStatus.ToDo:
        return 'À faire';
      case TaskStatus.InProgress:
        return 'En cours';
      case TaskStatus.Done:
        return 'Terminé';
      case TaskStatus.Blocked:
        return 'Bloqué';

      // Statuts MOE
      case TaskStatus.Esquisse:
        return 'Esquisse (15%)';
      case TaskStatus.AvantProjetSommaire:
        return 'Avant-Projet Sommaire (25%)';
      case TaskStatus.AvantProjetDefinitif:
        return 'Avant-Projet Définitif (35%)';
      case TaskStatus.DossierConsultation:
        return 'Dossier de Consultation (45%)';
      case TaskStatus.AnalyseOffres:
        return 'Analyse des Offres (55%)';
      case TaskStatus.SuiviChantier:
        return 'Suivi de Chantier (70%)';
      case TaskStatus.Reception:
        return 'Réception (90%)';
    }
  }

  /// Retourne la couleur correspondant au statut d'une tâche
  Color _getTaskStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.ToDo:
        return Colors.grey;
      case TaskStatus.InProgress:
        return Colors.blue;
      case TaskStatus.Done:
        return Colors.green;
      case TaskStatus.Blocked:
        return Colors.red;
      case TaskStatus.Esquisse:
        return Colors.purple;
      case TaskStatus.AvantProjetSommaire:
        return Colors.deepPurple;
      case TaskStatus.AvantProjetDefinitif:
        return Colors.indigo;
      case TaskStatus.DossierConsultation:
        return Colors.lightBlue;
      case TaskStatus.AnalyseOffres:
        return Colors.cyan;
      case TaskStatus.SuiviChantier:
        return Colors.teal;
      case TaskStatus.Reception:
        return Colors.green.shade700;
    }
  }

  /// Retourne la couleur correspondant au statut d'un élément de rapport
  Color _getStatusColor(String status) {
    switch (status) {
      case 'TODO':
        return Colors.orange;
      case 'IN_PROGRESS':
        return Colors.blue;
      case 'DONE':
        return Colors.green;
      case 'VALIDATED':
        return Colors.purple;
      case 'INFO':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// Retourne le nom d'affichage du statut d'un élément de rapport
  String _getStatusName(String status) {
    switch (status) {
      case 'TODO':
        return 'À faire';
      case 'IN_PROGRESS':
        return 'En cours';
      case 'DONE':
        return 'Terminé';
      case 'VALIDATED':
        return 'Validé';
      case 'INFO':
        return 'Information';
      default:
        return 'Inconnu';
    }
  }
}
