import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:seqqo/features/planning/models/planning_task.dart';
import 'package:seqqo/features/planning/models/planning_data.dart';
import 'package:seqqo/features/planning/models/planning_change_log.dart';
import 'package:seqqo/features/planning/models/planning_baseline.dart';
import 'package:seqqo/core/services/logging_service.dart';

/// Classe pour gérer le cache avec TTL
class _CacheEntry<T> {
  final List<T> data;
  final DateTime timestamp;
  final Duration ttl;

  _CacheEntry({
    required this.data,
    required this.timestamp,
    this.ttl = const Duration(minutes: 5),
  });

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;
}

/// Service pour gérer le stockage des tâches du planning
class PlanningStorageService {
  final FirebaseFirestore _firestore;
  final String _planningId;
  final Uuid _uuid = const Uuid();
  final _logger = LoggingService.getLogger('PlanningStorageService');

  // L'ID de l'organisation actuelle
  String? _currentOrganizationId;

  // Collection References
  late CollectionReference<PlanningData> _planningsRef;
  late CollectionReference<PlanningTask> _tasksRef;
  late CollectionReference<PlanningChangeLog> _changeLogsRef;
  late CollectionReference<Map<String, dynamic>> _baselinesRef;

  PlanningStorageService({
    required FirebaseFirestore firestore,
    required String planningId,
  })  : _firestore = firestore,
        _planningId = planningId {
    _initCollections();
    _loadCurrentOrganization();
  }

  CollectionReference<Map<String, dynamic>> get _tasksCollection =>
      _firestore.collection('plannings').doc(_planningId).collection('tasks');

  /// Initialise les collections Firestore
  Future<void> _loadCurrentOrganization() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentOrganizationId = prefs.getString('current_organization_id');

      if (_currentOrganizationId != null) {
        _logger.info('Organisation actuelle chargée: $_currentOrganizationId');
      } else {
        _logger.info('Aucune organisation actuelle définie');
      }

      // Réinitialiser les collections avec le nouvel ID d'organisation
      _initCollections();
    } catch (e) {
      _logger
          .warning('Erreur lors du chargement de l\'organisation actuelle: $e');
    }
  }

  /// Initialise les références de collection en fonction de l'organisation actuelle
  void _initCollections() {
    if (_currentOrganizationId != null) {
      // Utiliser les collections spécifiques à l'organisation
      final orgPath = 'organizations/$_currentOrganizationId';

      _planningsRef = _firestore
          .collection('$orgPath/plannings')
          .withConverter<PlanningData>(
            fromFirestore: (snapshots, _) =>
                PlanningData.fromJson(snapshots.data()!),
            toFirestore: (planningData, _) => planningData.toJson(),
          );

      _tasksRef = _firestore
          .collection('$orgPath/planningTasks')
          .withConverter<PlanningTask>(
            fromFirestore: (snapshots, _) =>
                PlanningTask.fromJson(snapshots.data()!),
            toFirestore: (task, _) => task.toJson(),
          );

      _changeLogsRef = _firestore
          .collection('$orgPath/planningChangeLogs')
          .withConverter<PlanningChangeLog>(
            fromFirestore: (snapshots, _) =>
                PlanningChangeLog.fromJson(snapshots.data()!),
            toFirestore: (log, _) => log.toJson(),
          );

      _baselinesRef = _firestore.collection('$orgPath/planningBaselines');

      _logger.info(
          'Collections initialisées pour l\'organisation: $_currentOrganizationId');
    } else {
      // Utiliser les collections globales (pour la rétrocompatibilité)
      _planningsRef =
          _firestore.collection('plannings').withConverter<PlanningData>(
                fromFirestore: (snapshots, _) =>
                    PlanningData.fromJson(snapshots.data()!),
                toFirestore: (planningData, _) => planningData.toJson(),
              );

      _tasksRef =
          _firestore.collection('planningTasks').withConverter<PlanningTask>(
                fromFirestore: (snapshots, _) =>
                    PlanningTask.fromJson(snapshots.data()!),
                toFirestore: (task, _) => task.toJson(),
              );

      _changeLogsRef = _firestore
          .collection('planningChangeLogs')
          .withConverter<PlanningChangeLog>(
            fromFirestore: (snapshots, _) =>
                PlanningChangeLog.fromJson(snapshots.data()!),
            toFirestore: (log, _) => log.toJson(),
          );

      _baselinesRef = _firestore.collection('planningBaselines');

      _logger.info('Collections initialisées sans organisation spécifique');
    }
  }

  /// Récupère les tâches avec pagination
  Future<List<PlanningTask>> getTasks({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    TaskStatus? status,
    String? searchQuery,
  }) async {
    try {
      _logger.info('Récupération des tâches pour le planning $_planningId');
      _logger.info('Collection path: plannings/$_planningId/tasks');

      Query<Map<String, dynamic>> query = _tasksCollection;

      if (status != null) {
        query = query.where('status', isEqualTo: status.index);
        _logger.info('Filtre par statut: ${status.index}');
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('taskName', isGreaterThanOrEqualTo: searchQuery)
            .where('taskName', isLessThanOrEqualTo: '$searchQuery\uf8ff');
        _logger.info('Filtre par recherche: $searchQuery');
      }

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
        _logger.info('Pagination à partir du document: ${lastDocument.id}');
      }

      _logger.info('Exécution de la requête avec limite: $limit');
      final querySnapshot = await query.limit(limit).get();
      _logger.info('${querySnapshot.docs.length} documents trouvés');

      final tasks = querySnapshot.docs
          .map((doc) => PlanningTask.fromJson(doc.data()))
          .toList();

      _logger.info('${tasks.length} tâches converties avec succès');
      return tasks;
    } catch (e) {
      _logger.severe('Erreur lors de la récupération des tâches: $e');
      throw Exception('Erreur lors de la récupération des tâches: $e');
    }
  }

  /// Récupère une tâche spécifique
  Future<PlanningTask?> getTask(String taskId) async {
    try {
      final doc = await _tasksCollection.doc(taskId).get();
      if (!doc.exists) return null;
      return PlanningTask.fromJson(doc.data()!);
    } catch (e) {
      throw Exception('Erreur lors de la récupération de la tâche: $e');
    }
  }

  /// Ajoute une nouvelle tâche
  Future<PlanningTask> addTask(PlanningTask task) async {
    try {
      final docRef = _tasksCollection.doc();
      final newTask = task.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await docRef.set(newTask.toJson());
      return newTask;
    } catch (e) {
      throw Exception('Erreur lors de l\'ajout de la tâche: $e');
    }
  }

  /// Met à jour une tâche existante
  Future<PlanningTask> updateTask(PlanningTask task) async {
    try {
      final updatedTask = task.copyWith(updatedAt: DateTime.now());
      await _tasksCollection.doc(task.id).update(updatedTask.toJson());
      return updatedTask;
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de la tâche: $e');
    }
  }

  /// Supprime une tâche
  Future<void> deleteTask(String taskId) async {
    try {
      await _tasksCollection.doc(taskId).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression de la tâche: $e');
    }
  }

  /// Met à jour le statut d'une tâche
  Future<PlanningTask> updateTaskStatus(
      String taskId, TaskStatus status) async {
    try {
      final doc = await _tasksCollection.doc(taskId).get();
      if (!doc.exists) {
        throw Exception('Tâche non trouvée');
      }
      final task = PlanningTask.fromJson(doc.data()!);
      final updatedTask = task.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      await _tasksCollection.doc(taskId).update(updatedTask.toJson());
      return updatedTask;
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  /// Met à jour la progression d'une tâche
  Future<PlanningTask> updateTaskProgress(
      String taskId, int progressPercentage) async {
    try {
      final doc = await _tasksCollection.doc(taskId).get();
      if (!doc.exists) {
        throw Exception('Tâche non trouvée');
      }
      final task = PlanningTask.fromJson(doc.data()!);
      final updatedTask = task.copyWith(
        progressPercentage: progressPercentage,
        updatedAt: DateTime.now(),
      );
      await _tasksCollection.doc(taskId).update(updatedTask.toJson());
      return updatedTask;
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de la progression: $e');
    }
  }

  // --- Change Log Operations ---

  /// Ajoute une entrée dans le journal des modifications
  Future<void> _addChangeLogEntry(PlanningChangeLog logEntry) async {
    try {
      await _changeLogsRef.doc(logEntry.logId).set(logEntry);
      _logger.info(
          'Entrée de journal ajoutée: ${logEntry.logId} - ${logEntry.changeType}');
    } catch (e) {
      _logger.warning('Erreur lors de l\'ajout d\'une entrée de journal: $e');
    }
  }

  /// Récupère l'historique des modifications pour un planning
  Future<List<PlanningChangeLog>> getPlanningHistory() async {
    try {
      await _loadCurrentOrganization();

      Query<PlanningChangeLog> query = _changeLogsRef
          .where('planningId', isEqualTo: _planningId)
          .orderBy('timestamp', descending: true);

      final querySnapshot = await query.get();
      final results = querySnapshot.docs.map((doc) => doc.data()).toList();

      _logger.info(
          '${results.length} entrées d\'historique trouvées pour le planning $_planningId');
      return results;
    } catch (e) {
      _logger.warning(
          'Erreur lors de la récupération de l\'historique du planning: $e');
      return [];
    }
  }

  // --- Baseline Operations ---

  /// Crée une nouvelle baseline (snapshot) du planning actuel
  Future<String?> createBaseline(
      String name, String description, String userId) async {
    try {
      await _loadCurrentOrganization();

      final tasks = await getTasks();
      _logger.info(
          '${tasks.length} tâches récupérées pour la création de la baseline');

      final baselineId = _uuid.v4();

      final baseline = {
        'baselineId': baselineId,
        'planningId': _planningId,
        'name': name,
        'description': description,
        'createdAt': Timestamp.now(),
        'tasks': tasks.map((task) => task.toJson()).toList(),
        'createdBy': userId,
        'organizationId': _currentOrganizationId,
      };

      await _baselinesRef.doc(baselineId).set(baseline);
      _logger.info('Baseline créée: $baselineId - $name');

      final logEntry = PlanningChangeLog(
        logId: _uuid.v4(),
        planningId: _planningId,
        timestamp: Timestamp.now(),
        userId: userId,
        changeType: 'BASELINE_CREATED',
        taskId: '',
        details: {'baselineName': name, 'baselineId': baselineId},
      );
      await _changeLogsRef.doc(logEntry.logId).set(logEntry);

      return baselineId;
    } catch (e) {
      _logger.severe(
          'Erreur lors de la création de la baseline pour le planning $_planningId: $e');
      return null;
    }
  }

  /// Récupère toutes les baselines pour un planning
  Future<List<PlanningBaseline>> getBaselinesForPlanning(
      String planningId) async {
    try {
      await _loadCurrentOrganization();
      final querySnapshot =
          await _baselinesRef.where('planningId', isEqualTo: planningId).get();
      return querySnapshot.docs
          .map((doc) => PlanningBaseline.fromJson(doc.data()))
          .toList();
    } catch (e) {
      _logger.severe(
          'Erreur lors de la récupération des baselines pour le planning $planningId: $e');
      return [];
    }
  }

  /// Récupère une baseline spécifique par son ID
  Future<PlanningBaseline?> getBaselineById(String baselineId) async {
    try {
      await _loadCurrentOrganization();

      final docSnapshot = await _baselinesRef.doc(baselineId).get();
      if (docSnapshot.exists) {
        final baseline = PlanningBaseline.fromFirestore(docSnapshot);
        _logger.info('Baseline récupérée: $baselineId - ${baseline.name}');
        return baseline;
      }
      _logger.warning('Baseline non trouvée: $baselineId');
      return null;
    } catch (e) {
      _logger.severe(
          'Erreur lors de la récupération de la baseline $baselineId: $e');
      return null;
    }
  }

  /// Restaure un planning à partir d'une baseline
  Future<bool> restoreFromBaseline(String baselineId, String userId) async {
    try {
      await _loadCurrentOrganization();

      final baseline = await getBaselineById(baselineId);
      if (baseline == null) {
        _logger.warning(
            'Impossible de restaurer: baseline $baselineId non trouvée');
        return false;
      }

      _logger.info(
          'Début de la restauration du planning $_planningId depuis la baseline ${baseline.name}');

      final currentTasks = await getTasks();
      _logger.info('Suppression de ${currentTasks.length} tâches existantes');

      for (var task in currentTasks) {
        await _tasksCollection.doc(task.id).delete();
      }

      _logger.info('Création de ${baseline.tasks.length} nouvelles tâches');
      for (var task in baseline.tasks) {
        final newTaskId = _uuid.v4();
        final newTask = task.copyWith(
          id: newTaskId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await _tasksCollection.doc(newTaskId).set(newTask.toJson());
      }

      final logEntry = PlanningChangeLog(
        logId: _uuid.v4(),
        planningId: _planningId,
        timestamp: Timestamp.now(),
        userId: userId,
        changeType: 'BASELINE_RESTORED',
        taskId: '',
        details: {
          'baselineName': baseline.name,
          'baselineId': baselineId,
          'tasksCount': baseline.tasks.length.toString(),
        },
      );
      await _changeLogsRef.doc(logEntry.logId).set(logEntry);

      _logger.info(
          'Planning $_planningId restauré avec succès depuis la baseline ${baseline.name}');
      return true;
    } catch (e) {
      _logger.severe(
          'Erreur lors de la restauration depuis la baseline $baselineId: $e');
      return false;
    }
  }

  /// Compare le planning actuel avec une baseline
  Future<Map<String, dynamic>> compareWithBaseline(String baselineId) async {
    try {
      final baseline = await getBaselineById(baselineId);
      if (baseline == null) {
        return {'error': 'Baseline not found'};
      }

      final currentTasks = await getTasks();

      final Map<String, dynamic> comparison = {
        'baselineName': baseline.name,
        'baselineDate': baseline.createdAt,
        'newTasks': [],
        'removedTasks': [],
        'modifiedTasks': [],
        'delayedTasks': [],
        'advancedTasks': [],
        'summary': {
          'totalTasksBaseline': baseline.tasks.length,
          'totalTasksCurrent': currentTasks.length,
          'newTasksCount': 0,
          'removedTasksCount': 0,
          'modifiedTasksCount': 0,
          'delayedTasksCount': 0,
          'advancedTasksCount': 0,
        }
      };

      for (var baselineTask in baseline.tasks) {
        final currentTask = currentTasks.firstWhere(
          (t) => t.taskName == baselineTask.taskName,
          orElse: () => PlanningTask(
            id: '',
            taskName: '',
            planningId: _planningId,
          ),
        );

        if (currentTask.id.isEmpty) {
          comparison['removedTasks'].add(baselineTask.toJson());
          comparison['summary']['removedTasksCount']++;
        } else {
          bool isModified = false;
          final Map<String, dynamic> changes = {};

          if (baselineTask.startDate != currentTask.startDate) {
            changes['startDate'] = {
              'baseline': baselineTask.startDate,
              'current': currentTask.startDate,
            };
            isModified = true;

            if (currentTask.startDate != null &&
                baselineTask.startDate != null) {
              final diff = currentTask.startDate!
                  .difference(baselineTask.startDate!)
                  .inDays;
              if (diff > 0) {
                comparison['delayedTasks'].add({
                  'task': currentTask.toJson(),
                  'delayDays': diff,
                });
                comparison['summary']['delayedTasksCount']++;
              } else if (diff < 0) {
                comparison['advancedTasks'].add({
                  'task': currentTask.toJson(),
                  'advanceDays': -diff,
                });
                comparison['summary']['advancedTasksCount']++;
              }
            }
          }

          if (baselineTask.endDate != currentTask.endDate) {
            changes['endDate'] = {
              'baseline': baselineTask.endDate,
              'current': currentTask.endDate,
            };
            isModified = true;
          }

          if (baselineTask.status != currentTask.status) {
            changes['status'] = {
              'baseline': baselineTask.status.toString(),
              'current': currentTask.status.toString(),
            };
            isModified = true;
          }

          if (baselineTask.progressPercentage !=
              currentTask.progressPercentage) {
            changes['progressPercentage'] = {
              'baseline': baselineTask.progressPercentage,
              'current': currentTask.progressPercentage,
            };
            isModified = true;
          }

          if (isModified) {
            comparison['modifiedTasks'].add({
              'baselineTask': baselineTask.toJson(),
              'currentTask': currentTask.toJson(),
              'changes': changes,
            });
            comparison['summary']['modifiedTasksCount']++;
          }
        }
      }

      for (var currentTask in currentTasks) {
        final baselineTask = baseline.tasks.firstWhere(
          (t) => t.taskName == currentTask.taskName,
          orElse: () => PlanningTask(
            id: '',
            taskName: '',
            planningId: _planningId,
          ),
        );

        if (baselineTask.id.isEmpty) {
          comparison['newTasks'].add(currentTask.toJson());
          comparison['summary']['newTasksCount']++;
        }
      }

      _logger.info('Comparaison terminée avec succès');
      return comparison;
    } catch (e) {
      _logger.severe(
          'Erreur lors de la comparaison avec la baseline $baselineId: $e');
      return {'error': e.toString()};
    }
  }

  /// Récupère un stream des tâches pour un planning spécifique
  Stream<List<PlanningTask>> getTasksForPlanningStream() {
    try {
      _logger
          .info('Démarrage du stream de tâches pour le planning $_planningId');

      Query<Map<String, dynamic>> query =
          _tasksCollection.orderBy('startDate', descending: false);

      return query.snapshots().map((snapshot) {
        final tasks = snapshot.docs
            .map((doc) => PlanningTask.fromJson(doc.data()))
            .toList();

        _logger.info('${tasks.length} tâches récupérées dans le stream');
        return tasks;
      });
    } catch (e) {
      _logger.severe('Erreur lors de la création du stream de tâches: $e');
      // En cas d'erreur, retourner un stream vide
      return Stream.value([]);
    }
  }

  /// Migre un planning vers une organisation
  Future<void> migrateToOrganization(
      String projectId, String organizationId) async {
    try {
      _logger.info(
          'Migration du planning $_planningId vers l\'organisation $organizationId');

      // Cette méthode peut être implémentée selon les besoins spécifiques
      // Pour l'instant, on log simplement l'opération
      _logger.info('Migration terminée pour le planning $_planningId');
    } catch (e) {
      _logger
          .severe('Erreur lors de la migration du planning $_planningId: $e');
      rethrow;
    }
  }

  /// Crée un nouveau planning pour un projet
  Future<String?> createPlanning({
    required String projectId,
    required String projectName,
  }) async {
    try {
      await _loadCurrentOrganization();

      final planningId = _uuid.v4();

      final planningData = PlanningData(
        planningId: planningId,
        projectId: projectId,
        name: 'Planning pour $projectName',
      );

      // Créer le planning dans Firestore
      await _planningsRef.doc(planningId).set(planningData);

      _logger.info(
          'Planning créé avec succès: $planningId pour le projet $projectId');
      return planningId;
    } catch (e) {
      _logger.severe('Erreur lors de la création du planning: $e');
      return null;
    }
  }
}
