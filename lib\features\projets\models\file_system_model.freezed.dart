// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'file_system_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FileSystemItem _$FileSystemItemFromJson(Map<String, dynamic> json) {
  return _FileSystemItem.fromJson(json);
}

/// @nodoc
mixin _$FileSystemItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  FileSystemItemType get type => throw _privateConstructorUsedError;
  String get parentId => throw _privateConstructorUsedError;
  String? get fileUrl => throw _privateConstructorUsedError;
  String? get filePath => throw _privateConstructorUsedError;
  int? get fileSize => throw _privateConstructorUsedError;
  List<String> get childrenIds => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: true, includeToJson: false)
  List<FileSystemItem> get children => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  Timestamp? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  Timestamp? get updatedAt => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;

  /// Serializes this FileSystemItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FileSystemItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FileSystemItemCopyWith<FileSystemItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileSystemItemCopyWith<$Res> {
  factory $FileSystemItemCopyWith(
          FileSystemItem value, $Res Function(FileSystemItem) then) =
      _$FileSystemItemCopyWithImpl<$Res, FileSystemItem>;
  @useResult
  $Res call(
      {String id,
      String name,
      FileSystemItemType type,
      String parentId,
      String? fileUrl,
      String? filePath,
      int? fileSize,
      List<String> childrenIds,
      @JsonKey(includeFromJson: true, includeToJson: false)
      List<FileSystemItem> children,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      Timestamp? createdAt,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      Timestamp? updatedAt,
      bool isDeleted});
}

/// @nodoc
class _$FileSystemItemCopyWithImpl<$Res, $Val extends FileSystemItem>
    implements $FileSystemItemCopyWith<$Res> {
  _$FileSystemItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileSystemItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? parentId = null,
    Object? fileUrl = freezed,
    Object? filePath = freezed,
    Object? fileSize = freezed,
    Object? childrenIds = null,
    Object? children = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as FileSystemItemType,
      parentId: null == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: freezed == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      filePath: freezed == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String?,
      fileSize: freezed == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int?,
      childrenIds: null == childrenIds
          ? _value.childrenIds
          : childrenIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      children: null == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as List<FileSystemItem>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as Timestamp?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as Timestamp?,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FileSystemItemImplCopyWith<$Res>
    implements $FileSystemItemCopyWith<$Res> {
  factory _$$FileSystemItemImplCopyWith(_$FileSystemItemImpl value,
          $Res Function(_$FileSystemItemImpl) then) =
      __$$FileSystemItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      FileSystemItemType type,
      String parentId,
      String? fileUrl,
      String? filePath,
      int? fileSize,
      List<String> childrenIds,
      @JsonKey(includeFromJson: true, includeToJson: false)
      List<FileSystemItem> children,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      Timestamp? createdAt,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      Timestamp? updatedAt,
      bool isDeleted});
}

/// @nodoc
class __$$FileSystemItemImplCopyWithImpl<$Res>
    extends _$FileSystemItemCopyWithImpl<$Res, _$FileSystemItemImpl>
    implements _$$FileSystemItemImplCopyWith<$Res> {
  __$$FileSystemItemImplCopyWithImpl(
      _$FileSystemItemImpl _value, $Res Function(_$FileSystemItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileSystemItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? parentId = null,
    Object? fileUrl = freezed,
    Object? filePath = freezed,
    Object? fileSize = freezed,
    Object? childrenIds = null,
    Object? children = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isDeleted = null,
  }) {
    return _then(_$FileSystemItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as FileSystemItemType,
      parentId: null == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: freezed == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      filePath: freezed == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String?,
      fileSize: freezed == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int?,
      childrenIds: null == childrenIds
          ? _value._childrenIds
          : childrenIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      children: null == children
          ? _value._children
          : children // ignore: cast_nullable_to_non_nullable
              as List<FileSystemItem>,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as Timestamp?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as Timestamp?,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FileSystemItemImpl extends _FileSystemItem {
  const _$FileSystemItemImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.parentId,
      this.fileUrl,
      this.filePath,
      this.fileSize,
      final List<String> childrenIds = const [],
      @JsonKey(includeFromJson: true, includeToJson: false)
      final List<FileSystemItem> children = const [],
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      this.createdAt,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      this.updatedAt,
      this.isDeleted = false})
      : _childrenIds = childrenIds,
        _children = children,
        super._();

  factory _$FileSystemItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileSystemItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final FileSystemItemType type;
  @override
  final String parentId;
  @override
  final String? fileUrl;
  @override
  final String? filePath;
  @override
  final int? fileSize;
  final List<String> _childrenIds;
  @override
  @JsonKey()
  List<String> get childrenIds {
    if (_childrenIds is EqualUnmodifiableListView) return _childrenIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_childrenIds);
  }

  final List<FileSystemItem> _children;
  @override
  @JsonKey(includeFromJson: true, includeToJson: false)
  List<FileSystemItem> get children {
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_children);
  }

  @override
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  final Timestamp? createdAt;
  @override
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  final Timestamp? updatedAt;
  @override
  @JsonKey()
  final bool isDeleted;

  @override
  String toString() {
    return 'FileSystemItem(id: $id, name: $name, type: $type, parentId: $parentId, fileUrl: $fileUrl, filePath: $filePath, fileSize: $fileSize, childrenIds: $childrenIds, children: $children, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileSystemItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.fileUrl, fileUrl) || other.fileUrl == fileUrl) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            const DeepCollectionEquality()
                .equals(other._childrenIds, _childrenIds) &&
            const DeepCollectionEquality().equals(other._children, _children) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      parentId,
      fileUrl,
      filePath,
      fileSize,
      const DeepCollectionEquality().hash(_childrenIds),
      const DeepCollectionEquality().hash(_children),
      createdAt,
      updatedAt,
      isDeleted);

  /// Create a copy of FileSystemItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileSystemItemImplCopyWith<_$FileSystemItemImpl> get copyWith =>
      __$$FileSystemItemImplCopyWithImpl<_$FileSystemItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileSystemItemImplToJson(
      this,
    );
  }
}

abstract class _FileSystemItem extends FileSystemItem {
  const factory _FileSystemItem(
      {required final String id,
      required final String name,
      required final FileSystemItemType type,
      required final String parentId,
      final String? fileUrl,
      final String? filePath,
      final int? fileSize,
      final List<String> childrenIds,
      @JsonKey(includeFromJson: true, includeToJson: false)
      final List<FileSystemItem> children,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      final Timestamp? createdAt,
      @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
      final Timestamp? updatedAt,
      final bool isDeleted}) = _$FileSystemItemImpl;
  const _FileSystemItem._() : super._();

  factory _FileSystemItem.fromJson(Map<String, dynamic> json) =
      _$FileSystemItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  FileSystemItemType get type;
  @override
  String get parentId;
  @override
  String? get fileUrl;
  @override
  String? get filePath;
  @override
  int? get fileSize;
  @override
  List<String> get childrenIds;
  @override
  @JsonKey(includeFromJson: true, includeToJson: false)
  List<FileSystemItem> get children;
  @override
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  Timestamp? get createdAt;
  @override
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  Timestamp? get updatedAt;
  @override
  bool get isDeleted;

  /// Create a copy of FileSystemItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileSystemItemImplCopyWith<_$FileSystemItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FileSystemState {
  List<FileSystemItem> get items => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  int get storageUsed => throw _privateConstructorUsedError;
  List<String> get selectedItemIds => throw _privateConstructorUsedError;
  String? get currentFolderId => throw _privateConstructorUsedError;
  bool get isGridView => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;
  bool get hasMoreItems => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  int get totalItems => throw _privateConstructorUsedError;
  String get searchQuery => throw _privateConstructorUsedError;
  Set<String> get selectedFilters => throw _privateConstructorUsedError;
  bool get showFilters => throw _privateConstructorUsedError;

  /// Create a copy of FileSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FileSystemStateCopyWith<FileSystemState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileSystemStateCopyWith<$Res> {
  factory $FileSystemStateCopyWith(
          FileSystemState value, $Res Function(FileSystemState) then) =
      _$FileSystemStateCopyWithImpl<$Res, FileSystemState>;
  @useResult
  $Res call(
      {List<FileSystemItem> items,
      bool isLoading,
      String? error,
      int storageUsed,
      List<String> selectedItemIds,
      String? currentFolderId,
      bool isGridView,
      DateTime? lastUpdated,
      bool hasMoreItems,
      int currentPage,
      int pageSize,
      int totalItems,
      String searchQuery,
      Set<String> selectedFilters,
      bool showFilters});
}

/// @nodoc
class _$FileSystemStateCopyWithImpl<$Res, $Val extends FileSystemState>
    implements $FileSystemStateCopyWith<$Res> {
  _$FileSystemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FileSystemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? storageUsed = null,
    Object? selectedItemIds = null,
    Object? currentFolderId = freezed,
    Object? isGridView = null,
    Object? lastUpdated = freezed,
    Object? hasMoreItems = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? totalItems = null,
    Object? searchQuery = null,
    Object? selectedFilters = null,
    Object? showFilters = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<FileSystemItem>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      storageUsed: null == storageUsed
          ? _value.storageUsed
          : storageUsed // ignore: cast_nullable_to_non_nullable
              as int,
      selectedItemIds: null == selectedItemIds
          ? _value.selectedItemIds
          : selectedItemIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      currentFolderId: freezed == currentFolderId
          ? _value.currentFolderId
          : currentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      isGridView: null == isGridView
          ? _value.isGridView
          : isGridView // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasMoreItems: null == hasMoreItems
          ? _value.hasMoreItems
          : hasMoreItems // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedFilters: null == selectedFilters
          ? _value.selectedFilters
          : selectedFilters // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      showFilters: null == showFilters
          ? _value.showFilters
          : showFilters // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FileSystemStateImplCopyWith<$Res>
    implements $FileSystemStateCopyWith<$Res> {
  factory _$$FileSystemStateImplCopyWith(_$FileSystemStateImpl value,
          $Res Function(_$FileSystemStateImpl) then) =
      __$$FileSystemStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FileSystemItem> items,
      bool isLoading,
      String? error,
      int storageUsed,
      List<String> selectedItemIds,
      String? currentFolderId,
      bool isGridView,
      DateTime? lastUpdated,
      bool hasMoreItems,
      int currentPage,
      int pageSize,
      int totalItems,
      String searchQuery,
      Set<String> selectedFilters,
      bool showFilters});
}

/// @nodoc
class __$$FileSystemStateImplCopyWithImpl<$Res>
    extends _$FileSystemStateCopyWithImpl<$Res, _$FileSystemStateImpl>
    implements _$$FileSystemStateImplCopyWith<$Res> {
  __$$FileSystemStateImplCopyWithImpl(
      _$FileSystemStateImpl _value, $Res Function(_$FileSystemStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FileSystemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? storageUsed = null,
    Object? selectedItemIds = null,
    Object? currentFolderId = freezed,
    Object? isGridView = null,
    Object? lastUpdated = freezed,
    Object? hasMoreItems = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? totalItems = null,
    Object? searchQuery = null,
    Object? selectedFilters = null,
    Object? showFilters = null,
  }) {
    return _then(_$FileSystemStateImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<FileSystemItem>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      storageUsed: null == storageUsed
          ? _value.storageUsed
          : storageUsed // ignore: cast_nullable_to_non_nullable
              as int,
      selectedItemIds: null == selectedItemIds
          ? _value._selectedItemIds
          : selectedItemIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      currentFolderId: freezed == currentFolderId
          ? _value.currentFolderId
          : currentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      isGridView: null == isGridView
          ? _value.isGridView
          : isGridView // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasMoreItems: null == hasMoreItems
          ? _value.hasMoreItems
          : hasMoreItems // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedFilters: null == selectedFilters
          ? _value._selectedFilters
          : selectedFilters // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      showFilters: null == showFilters
          ? _value.showFilters
          : showFilters // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$FileSystemStateImpl implements _FileSystemState {
  const _$FileSystemStateImpl(
      {final List<FileSystemItem> items = const [],
      this.isLoading = true,
      this.error,
      this.storageUsed = 0,
      final List<String> selectedItemIds = const [],
      this.currentFolderId,
      this.isGridView = false,
      this.lastUpdated,
      this.hasMoreItems = false,
      this.currentPage = 0,
      this.pageSize = 20,
      this.totalItems = 0,
      this.searchQuery = '',
      final Set<String> selectedFilters = const {},
      this.showFilters = false})
      : _items = items,
        _selectedItemIds = selectedItemIds,
        _selectedFilters = selectedFilters;

  final List<FileSystemItem> _items;
  @override
  @JsonKey()
  List<FileSystemItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  @override
  @JsonKey()
  final int storageUsed;
  final List<String> _selectedItemIds;
  @override
  @JsonKey()
  List<String> get selectedItemIds {
    if (_selectedItemIds is EqualUnmodifiableListView) return _selectedItemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedItemIds);
  }

  @override
  final String? currentFolderId;
  @override
  @JsonKey()
  final bool isGridView;
  @override
  final DateTime? lastUpdated;
  @override
  @JsonKey()
  final bool hasMoreItems;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int totalItems;
  @override
  @JsonKey()
  final String searchQuery;
  final Set<String> _selectedFilters;
  @override
  @JsonKey()
  Set<String> get selectedFilters {
    if (_selectedFilters is EqualUnmodifiableSetView) return _selectedFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedFilters);
  }

  @override
  @JsonKey()
  final bool showFilters;

  @override
  String toString() {
    return 'FileSystemState(items: $items, isLoading: $isLoading, error: $error, storageUsed: $storageUsed, selectedItemIds: $selectedItemIds, currentFolderId: $currentFolderId, isGridView: $isGridView, lastUpdated: $lastUpdated, hasMoreItems: $hasMoreItems, currentPage: $currentPage, pageSize: $pageSize, totalItems: $totalItems, searchQuery: $searchQuery, selectedFilters: $selectedFilters, showFilters: $showFilters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileSystemStateImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.storageUsed, storageUsed) ||
                other.storageUsed == storageUsed) &&
            const DeepCollectionEquality()
                .equals(other._selectedItemIds, _selectedItemIds) &&
            (identical(other.currentFolderId, currentFolderId) ||
                other.currentFolderId == currentFolderId) &&
            (identical(other.isGridView, isGridView) ||
                other.isGridView == isGridView) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.hasMoreItems, hasMoreItems) ||
                other.hasMoreItems == hasMoreItems) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            const DeepCollectionEquality()
                .equals(other._selectedFilters, _selectedFilters) &&
            (identical(other.showFilters, showFilters) ||
                other.showFilters == showFilters));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_items),
      isLoading,
      error,
      storageUsed,
      const DeepCollectionEquality().hash(_selectedItemIds),
      currentFolderId,
      isGridView,
      lastUpdated,
      hasMoreItems,
      currentPage,
      pageSize,
      totalItems,
      searchQuery,
      const DeepCollectionEquality().hash(_selectedFilters),
      showFilters);

  /// Create a copy of FileSystemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileSystemStateImplCopyWith<_$FileSystemStateImpl> get copyWith =>
      __$$FileSystemStateImplCopyWithImpl<_$FileSystemStateImpl>(
          this, _$identity);
}

abstract class _FileSystemState implements FileSystemState {
  const factory _FileSystemState(
      {final List<FileSystemItem> items,
      final bool isLoading,
      final String? error,
      final int storageUsed,
      final List<String> selectedItemIds,
      final String? currentFolderId,
      final bool isGridView,
      final DateTime? lastUpdated,
      final bool hasMoreItems,
      final int currentPage,
      final int pageSize,
      final int totalItems,
      final String searchQuery,
      final Set<String> selectedFilters,
      final bool showFilters}) = _$FileSystemStateImpl;

  @override
  List<FileSystemItem> get items;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  int get storageUsed;
  @override
  List<String> get selectedItemIds;
  @override
  String? get currentFolderId;
  @override
  bool get isGridView;
  @override
  DateTime? get lastUpdated;
  @override
  bool get hasMoreItems;
  @override
  int get currentPage;
  @override
  int get pageSize;
  @override
  int get totalItems;
  @override
  String get searchQuery;
  @override
  Set<String> get selectedFilters;
  @override
  bool get showFilters;

  /// Create a copy of FileSystemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileSystemStateImplCopyWith<_$FileSystemStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
