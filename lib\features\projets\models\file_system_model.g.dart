// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_system_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FileSystemItemImpl _$$FileSystemItemImplFromJson(Map<String, dynamic> json) =>
    _$FileSystemItemImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$FileSystemItemTypeEnumMap, json['type']),
      parentId: json['parentId'] as String,
      fileUrl: json['fileUrl'] as String?,
      filePath: json['filePath'] as String?,
      fileSize: (json['fileSize'] as num?)?.toInt(),
      childrenIds: (json['childrenIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      children: (json['children'] as List<dynamic>?)
              ?.map((e) => FileSystemItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: _timestampFromJson(json['createdAt']),
      updatedAt: _timestampFromJson(json['updatedAt']),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$FileSystemItemImplToJson(
        _$FileSystemItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$FileSystemItemTypeEnumMap[instance.type]!,
      'parentId': instance.parentId,
      'fileUrl': instance.fileUrl,
      'filePath': instance.filePath,
      'fileSize': instance.fileSize,
      'childrenIds': instance.childrenIds,
      'createdAt': _timestampToJson(instance.createdAt),
      'updatedAt': _timestampToJson(instance.updatedAt),
      'isDeleted': instance.isDeleted,
    };

const _$FileSystemItemTypeEnumMap = {
  FileSystemItemType.folder: 'folder',
  FileSystemItemType.file: 'file',
};
