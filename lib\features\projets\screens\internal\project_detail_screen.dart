import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/core/widgets/loading_indicator.dart';
import 'package:seqqo/features/planning/models/planning_task.dart';
import 'package:seqqo/features/projets/models/project_data.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/acceptance_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/audit_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/companies_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/dashboard_info_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/files_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/planning_tab.dart';
import 'package:seqqo/features/projets/screens/internal/tabs/report_tab.dart';
import 'package:seqqo/features/projets/services/project_storage_service.dart';
import 'package:seqqo/features/planning/services/planning_storage_service.dart';
import 'package:seqqo/services/logging_service.dart';

/// Onglets disponibles dans l'écran de détail du projet
enum ProjectTab {
  /// Onglet Dashboard et Informations
  dashboardInfo,

  /// Onglet Audits
  audit,

  /// Onglet Rapports
  report,

  /// Onglet Planning
  planning,

  /// Onglet Entreprises (Travaux)
  companies,

  /// Onglet PV de réception
  acceptance,

  /// Onglet Fichiers
  files,
}

/// Écran de détail du projet - Version optimisée
class ProjectDetailScreen extends ConsumerStatefulWidget {
  /// ID du projet
  final String projectId;

  /// Fonction appelée lors du retour
  final VoidCallback? onBack;

  /// Données initiales du projet (optionnel)
  final ProjectData? initialProjectData;

  /// Constructeur
  const ProjectDetailScreen({
    super.key,
    required this.projectId,
    this.onBack,
    this.initialProjectData,
  });

  @override
  ConsumerState<ProjectDetailScreen> createState() =>
      _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends ConsumerState<ProjectDetailScreen>
    with SingleTickerProviderStateMixin {
  final ProjectStorageService _projectService = ProjectStorageService();
  PlanningStorageService? _planningService;
  final _logger = LoggingService.getLogger('ProjectDetailScreen');

  bool _isLoading = true;
  ProjectData? _projectData;
  String? _errorMessage;
  late TabController _tabController;

  // Données pour les onglets
  List<PlanningTask> _projectTasks = [];
  bool _tasksLoading = true;

  // Configuration des onglets
  final List<Map<String, dynamic>> _tabs = [
    {
      'tab': ProjectTab.dashboardInfo,
      'label': 'Informations',
      'icon': Icons.dashboard_outlined,
    },
    {
      'tab': ProjectTab.audit,
      'label': 'Audits',
      'icon': Icons.assignment_outlined,
    },
    {
      'tab': ProjectTab.report,
      'label': 'Comptes Rendus',
      'icon': Icons.description_outlined,
    },
    {
      'tab': ProjectTab.planning,
      'label': 'Planning',
      'icon': Icons.calendar_today_outlined,
    },
    {
      'tab': ProjectTab.companies,
      'label': 'Travaux',
      'icon': Icons.business_outlined,
    },
    {
      'tab': ProjectTab.acceptance,
      'label': 'PV de Réception',
      'icon': Icons.fact_check_outlined,
    },
    {
      'tab': ProjectTab.files,
      'label': 'Fichiers',
      'icon': Icons.folder_outlined,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Si des données initiales sont fournies, les utiliser
    if (widget.initialProjectData != null) {
      _projectData = widget.initialProjectData;
      _isLoading = false;
    } else {
      // Sinon, charger le projet normalement
      _loadProject();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Charge les tâches du planning
  Future<void> _loadPlanningTasks(String planningId) async {
    _logger.info('Chargement des tâches du planning $planningId');
    setState(() {
      _tasksLoading = true;
    });

    try {
      _planningService = PlanningStorageService(
        firestore: FirebaseFirestore.instance,
        planningId: planningId,
      );
      final tasks = await _planningService?.getTasks() ?? [];

      if (mounted) {
        setState(() {
          _projectTasks = tasks;
          _tasksLoading = false;
        });
      }

      _logger
          .info('${tasks.length} tâches chargées pour le planning $planningId');
    } catch (e) {
      _logger.severe('Erreur lors du chargement des tâches du planning: $e');
      if (mounted) {
        setState(() {
          _projectTasks = [];
          _tasksLoading = false;
        });
      }
    }
  }

  Future<void> _loadProject() async {
    _logger.info('Chargement du projet ${widget.projectId}');
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Vérifier que l'ID du projet est valide
      if (widget.projectId.isEmpty) {
        _logger.warning('ID de projet vide');
        setState(() {
          _errorMessage = 'ID de projet invalide';
          _isLoading = false;
        });
        return;
      }

      // Charger le projet
      final project = await _projectService.loadProject(widget.projectId);

      if (project == null) {
        _logger.warning('Projet non trouvé');
        setState(() {
          _errorMessage =
              'Projet non trouvé. Vérifiez que vous avez accès à ce projet.';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _projectData = project;
        _isLoading = false;
      });

      _logger.info('Projet chargé avec succès: ${project.projectId}');

      // Charger les tâches du planning si le projet a un planning
      if (project.planningId != null) {
        _loadPlanningTasks(project.planningId!);
      } else {
        setState(() {
          _tasksLoading = false;
        });
      }
    } catch (e) {
      _logger.severe('Erreur lors du chargement du projet: $e');
      setState(() {
        _errorMessage = 'Erreur lors du chargement du projet: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isLoading
            ? const Text('Chargement du projet...')
            : Text(_projectData?.projectName ?? 'Projet sans nom'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: widget.onBack ?? () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Barre d'onglets style Notion
          _buildNotionLikeTabBar(theme),

          // Contenu principal
          Expanded(
            child: _buildBody(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotionLikeTabBar(ThemeData theme) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(
              red: theme.colorScheme.outline.r,
              green: theme.colorScheme.outline.g,
              blue: theme.colorScheme.outline.b,
              alpha: 0.2,
            ),
            width: 1,
          ),
        ),
      ),
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tab = entry.value;
            final isSelected = _tabController.index == index;

            return InkWell(
              onTap: () {
                _tabController.animateTo(index);
                setState(() {});
              },
              borderRadius: BorderRadius.circular(4),
              child: Container(
                margin: const EdgeInsets.only(right: 8, bottom: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primaryContainer.withValues(
                          red: theme.colorScheme.primaryContainer.r,
                          green: theme.colorScheme.primaryContainer.g,
                          blue: theme.colorScheme.primaryContainer.b,
                          alpha: 0.2,
                        )
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary.withValues(
                            red: theme.colorScheme.primary.r,
                            green: theme.colorScheme.primary.g,
                            blue: theme.colorScheme.primary.b,
                            alpha: 0.3,
                          )
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      tab['icon'],
                      size: 16,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      tab['label'],
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.w400,
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Erreur',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _loadProject,
                child: const Text('Réessayer'),
              ),
            ],
          ),
        ),
      );
    }

    // Utiliser un IndexedStack au lieu d'un TabBarView pour éviter les conflits
    // avec notre TabBar personnalisé
    return IndexedStack(
      index: _tabController.index,
      sizing: StackFit.expand,
      children: [
        _buildDashboardInfoTab(),
        _buildAuditTab(),
        _buildReportTab(),
        _buildPlanningTab(),
        _buildCompaniesTab(),
        _buildAcceptanceTab(),
        _buildFilesTab(),
      ],
    );
  }

  Widget _buildDashboardInfoTab() {
    return DashboardInfoTab(
      projectId: widget.projectId,
      projectData: _projectData!,
      projectTasks: _projectTasks,
      isTasksLoading: _tasksLoading,
      onProjectUpdated: () {
        _loadProject();
      },
    );
  }

  Widget _buildAuditTab() {
    return AuditTab(
      projectId: widget.projectId,
    );
  }

  Widget _buildReportTab() {
    return ReportTab(
      projectId: widget.projectId,
    );
  }

  Widget _buildPlanningTab() {
    return PlanningTab(
      projectId: widget.projectId,
      projectData: _projectData!,
      projectTasks: _projectTasks,
      isTasksLoading: _tasksLoading,
      onTasksUpdated: () {
        _loadProject();
      },
    );
  }

  Widget _buildCompaniesTab() {
    return CompaniesTab(
      projectId: widget.projectId,
      projectData: _projectData!,
    );
  }

  Widget _buildAcceptanceTab() {
    return AcceptanceTab(
      projectId: widget.projectId,
    );
  }

  Widget _buildFilesTab() {
    return FilesTab(
      projectId: widget.projectId,
    );
  }
}
