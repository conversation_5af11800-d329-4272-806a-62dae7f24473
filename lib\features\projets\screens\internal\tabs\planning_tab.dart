import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/core/widgets/empty_state.dart';
import 'package:seqqo/core/widgets/loading_indicator.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/features/planning/models/planning_task.dart';
import 'package:seqqo/features/planning/screens/enhanced_planning_screen.dart';
import 'package:seqqo/features/planning/screens/task_detail_screen.dart';
import 'package:seqqo/features/planning/widgets/baseline_management_dialog.dart';
import 'package:seqqo/features/planning/services/planning_storage_service.dart';
import 'package:seqqo/features/planning/widgets/time_scale_selector.dart';
import 'package:seqqo/features/planning/widgets/project_statistics_widget.dart';
import 'package:seqqo/features/planning/widgets/ai_help_dialog.dart';
import 'package:seqqo/features/projets/models/project_data.dart';
import 'package:seqqo/features/projets/services/project_storage_service.dart';

/// Onglet Planning du projet
class PlanningTab extends ConsumerStatefulWidget {
  /// ID du projet
  final String projectId;

  /// Données du projet
  final ProjectData projectData;

  /// Liste des tâches du projet
  final List<PlanningTask> projectTasks;

  /// Indique si les tâches sont en cours de chargement
  final bool isTasksLoading;

  /// Fonction appelée lorsque les tâches sont mises à jour
  final Function() onTasksUpdated;

  /// Constructeur
  const PlanningTab({
    super.key,
    required this.projectId,
    required this.projectData,
    required this.projectTasks,
    required this.isTasksLoading,
    required this.onTasksUpdated,
  });

  @override
  ConsumerState<PlanningTab> createState() => _PlanningTabState();
}

class _PlanningTabState extends ConsumerState<PlanningTab> {
  late final PlanningStorageService _planningService;

  // Variables pour la barre d'outils unifiée
  bool _todayButtonPressed = false;
  TimeScale _currentTimeScale = TimeScale.month;
  bool _isCondensedMode = false;
  String _searchQuery = '';

  // Référence à l'instance de EnhancedPlanningScreen
  EnhancedPlanningScreen? _planningScreen;

  // Callback pour déclencher l'export PDF depuis EnhancedPlanningScreen
  VoidCallback? _exportPdfCallback;

  // Contrôleur pour le champ de recherche
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _planningService = PlanningStorageService(
      firestore: FirebaseFirestore.instance,
      planningId: widget.projectData.planningId ??
          '', // Assurer que planningId est non-nul ou gérer le cas
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Méthode pour mettre à jour la recherche dans le planning
  void _updatePlanningSearch(String query) {
    // La recherche est déjà gérée par la variable _searchQuery
    // qui est passée au planning intégré
    // Cette méthode peut être étendue pour des fonctionnalités supplémentaires
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isTasksLoading) {
      return const Center(child: LoadingIndicator());
    }

    // Vérifier si le projet a un planning
    if (widget.projectData.planningId == null ||
        widget.projectData.planningId!.isEmpty) {
      return Center(
        child: EmptyState(
          icon: Icons.calendar_today_outlined,
          title: 'Aucun planning',
          message: 'Ce projet n\'a pas encore de planning.',
          actionLabel: 'Créer un planning',
          onAction: _createPlanning,
        ),
      );
    }

    // Afficher directement le planning en mode intégré
    // Cela permet à l'utilisateur de voir et d'interagir avec le planning sans avoir à cliquer sur un bouton
    // et évite les problèmes de navigation
    return _buildEmbeddedPlanningForm();
  }

  // Créer un nouveau planning pour le projet
  Future<void> _createPlanning() async {
    try {
      // Afficher un indicateur de chargement
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Création du planning en cours...')),
        );
      }

      // Créer un nouveau planning
      // TODO: Réviser la logique de création de planning
      final planningId = await _planningService.createPlanning(
        projectId: widget.projectId,
        projectName: widget.projectData.projectName ?? 'Projet sans nom',
      );

      if (planningId == null) {
        throw Exception('Échec de la création du planning');
      }

      // Mettre à jour le projet avec l'ID du planning
      final projectService = ProjectStorageService();
      final updatedProject =
          widget.projectData.copyWith(planningId: planningId);
      await projectService.saveProject(updatedProject);

      // Notifier le parent que le projet a été mis à jour
      widget.onTasksUpdated();

      // Attendre que les données du projet soient mises à jour
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Planning créé avec succès')),
        );

        // Rafraîchir l'interface pour afficher le planning
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création du planning: $e')),
        );
      }
    }
  }

  // Ajouter une nouvelle tâche
  void _addNewTask() {
    if (widget.projectData.planningId == null) return;

    // Tâche vide pour la création
    final emptyTask = PlanningTask(
      id: '',
      taskName: '',
      planningId: widget.projectData.planningId!,
      description: '',
      status: TaskStatus.ToDo,
      progressPercentage: 0,
      isMilestone: false,
      assignedTo: '',
    );

    // Afficher le formulaire d'ajout de tâche dans un modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (modalContext) => Center(
        child: FractionallySizedBox(
          widthFactor: 0.85,
          heightFactor: 0.8,
          child: Material(
            color: Theme.of(context).dialogTheme.backgroundColor,
            borderRadius: BorderRadius.circular(28),
            clipBehavior: Clip.antiAlias,
            child: TaskDetailScreen(
              task: emptyTask,
              onTaskUpdated: (newTask) {
                // Fermer le modal
                Navigator.of(modalContext).pop();

                // Rafraîchir la liste des tâches
                widget.onTasksUpdated();
              },
            ),
          ),
        ),
      ),
    );
  }

  // Construire le formulaire de planning intégré
  Widget _buildEmbeddedPlanningForm() {
    // Vérifier que le planning existe
    if (widget.projectData.planningId == null) {
      // Si le planning n'existe pas, afficher un message d'erreur
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Material(
          type: MaterialType.card,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Theme.of(context).colorScheme.outlineVariant,
              width: 1.0,
            ),
          ),
          color: Theme.of(context).colorScheme.surface,
          elevation: 0, // Réduction de l'élévation pour un style Notion
          child: Column(
            children: [
              // Barre d'outils simplifiée
              Builder(
                builder: (context) {
                  final theme = Theme.of(context);
                  return Container(
                    color: theme.colorScheme.surface,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 12.0),
                    child: Row(
                      children: [
                        // Bouton retour
                        AppButton.secondary(
                          icon: Icons.arrow_back,
                          text: '',
                          size: AppButtonSize.small,
                          tooltip: 'Retour',
                          onPressed: () {
                            // Rafraîchir l'interface
                            widget.onTasksUpdated();
                          },
                        ),
                        const SizedBox(width: 8),
                        // Titre avec style du thème
                        Text(
                          'Erreur',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            letterSpacing: -0.25,
                            fontFamily: 'RedditSans', // Police Notion-like
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const Divider(height: 1),
              // Message d'erreur
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Planning non disponible',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontFamily: 'RedditSans', // Police Notion-like
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Le planning n\'existe pas ou n\'est pas accessible.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontFamily: 'RedditSans', // Police Notion-like
                            ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _createPlanning,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          elevation: 0, // Style Notion sans élévation
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: const Text('Créer un planning'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Si le planning existe, afficher le formulaire de planning intégré
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Material(
        // Utilisation de Material pour garantir un rendu correct des coins arrondis
        type: MaterialType.card,
        clipBehavior:
            Clip.antiAlias, // Évite le débordement des éléments enfants
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant,
            width: 1.0,
          ),
        ),
        color: Theme.of(context).colorScheme.surface,
        elevation: 0, // Style Notion sans élévation
        child: Column(
          children: [
            // Barre d'outils personnalisée utilisant le thème
            Builder(
              builder: (context) {
                final theme = Theme.of(context);
                return Container(
                  color: theme.colorScheme.surface,
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 12.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Titre avec style du thème
                      Text(
                        'Planning du projet',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          letterSpacing: -0.25,
                          fontFamily: 'RedditSans', // Police Notion-like
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Champ de recherche avec style unifié
                      Container(
                        width: 200,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: theme.dividerColor.withAlpha(80),
                            width: 1,
                          ),
                        ),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Rechercher...',
                            prefixIcon: Icon(
                              Icons.search,
                              size: 16,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            border: InputBorder.none,
                            contentPadding:
                                const EdgeInsets.symmetric(vertical: 8),
                            hintStyle: theme.textTheme.bodyMedium?.copyWith(
                              fontFamily: 'RedditSans',
                              fontSize: 14, // Taille standardisée
                              color: theme.colorScheme.onSurfaceVariant
                                  .withAlpha(180),
                            ),
                          ),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontFamily: 'RedditSans',
                            fontSize: 14, // Taille standardisée
                          ),
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value.toLowerCase();
                            });
                            // Déclencher la recherche dans le planning intégré
                            _updatePlanningSearch(value);
                          },
                        ),
                      ),

                      const Spacer(),

                      // Statistiques du projet - version toolbar
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: ProjectStatisticsWidget(
                          tasks: widget.projectTasks,
                          isToolbarVersion: true,
                        ),
                      ),

                      // Bouton d'aide IA avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton.icon(
                            icon: Icon(
                              Icons.psychology_outlined,
                              size: 16,
                              color: theme.colorScheme.primary,
                            ),
                            label: Text(
                              'Aide IA',
                              style: TextStyle(
                                fontFamily: 'RedditSans',
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              side: BorderSide(
                                color: theme.colorScheme.primary
                                    .withValues(alpha: 0.3),
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              backgroundColor: theme
                                  .colorScheme.primaryContainer
                                  .withValues(alpha: 0.1),
                            ),
                            onPressed: () => AIHelpDialog.show(context),
                          ),
                        ),
                      ),

                      // Bouton "Aujourd'hui" avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton.icon(
                            icon: Icon(
                              Icons.today,
                              size: 16,
                              color: theme.colorScheme.primary,
                            ),
                            label: Text(
                              "Aujourd'hui",
                              style: TextStyle(
                                fontFamily: 'RedditSans',
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              side: BorderSide(
                                color: theme.dividerColor,
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            onPressed: () {
                              setState(() {
                                _todayButtonPressed = true;
                                Future.delayed(
                                    const Duration(milliseconds: 500), () {
                                  if (mounted) {
                                    setState(() {
                                      _todayButtonPressed = false;
                                    });
                                  }
                                });
                              });

                              // Afficher un feedback à l'utilisateur
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Navigation vers la date du jour...'),
                                  duration: Duration(seconds: 1),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // Sélecteur d'échelle temporelle avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: Container(
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surface,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: theme.dividerColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildTimeScaleButton(
                                    theme, TimeScale.week, 'S'),
                                _buildTimeScaleButton(
                                    theme, TimeScale.month, 'M'),
                                _buildTimeScaleButton(
                                    theme, TimeScale.quarter, 'T'),
                                _buildTimeScaleButton(
                                    theme, TimeScale.year, 'A'),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Bouton pour basculer entre le mode normal et condensé avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton.icon(
                            icon: Icon(
                              _isCondensedMode
                                  ? Icons.unfold_more
                                  : Icons.unfold_less,
                              size: 16,
                              color: _isCondensedMode
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface,
                            ),
                            label: Text(
                              _isCondensedMode ? 'Normal' : 'Condensé',
                              style: TextStyle(
                                fontFamily: 'RedditSans',
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: _isCondensedMode
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              side: BorderSide(
                                color: _isCondensedMode
                                    ? theme.colorScheme.primary
                                    : theme.dividerColor,
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              backgroundColor: _isCondensedMode
                                  ? theme.colorScheme.primaryContainer
                                      .withAlpha(40)
                                  : Colors.transparent,
                            ),
                            onPressed: () {
                              // Inverser l'état du mode condensé
                              setState(() {
                                _isCondensedMode = !_isCondensedMode;
                              });

                              // Afficher un message pour confirmer le changement
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(_isCondensedMode
                                      ? 'Mode condensé activé'
                                      : 'Mode normal activé'),
                                  duration: const Duration(seconds: 1),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // Bouton PDF avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton.icon(
                            icon: Icon(
                              Icons.picture_as_pdf_outlined,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            label: Text(
                              'PDF',
                              style: TextStyle(
                                fontFamily: 'RedditSans',
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              side: BorderSide(
                                color: theme.dividerColor,
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            onPressed: _exportPlanningToPdf,
                          ),
                        ),
                      ),

                      // Bouton Version avec style unifié
                      Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: SizedBox(
                          height: 32,
                          child: OutlinedButton.icon(
                            icon: Icon(
                              Icons.save_outlined,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            label: Text(
                              'Version',
                              style: TextStyle(
                                fontFamily: 'RedditSans',
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              side: BorderSide(
                                color: theme.dividerColor,
                                width: 1,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            onPressed: _navigateToBaselineManagement,
                          ),
                        ),
                      ),

                      // Bouton Ajouter une tâche avec style unifié
                      SizedBox(
                        height: 32,
                        child: ElevatedButton.icon(
                          icon: Icon(
                            Icons.add,
                            size: 16,
                            color: theme.colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Ajouter une tâche',
                            style: TextStyle(
                              fontFamily: 'RedditSans',
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          onPressed: _addNewTask,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            // Espacement entre la barre d'outils et le contenu
            const Divider(height: 1),
            // Contenu du formulaire (iframe ou widget intégré)
            Expanded(
              child: ClipRRect(
                // Assurer que le contenu ne déborde pas
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                child: Builder(
                  builder: (context) {
                    // Créer l'instance de EnhancedPlanningScreen
                    _planningScreen = EnhancedPlanningScreen(
                      planningId: widget.projectData.planningId!,
                      projectId: widget.projectId,
                      planningName: widget.projectData.projectName ??
                          'Planning du projet',
                      showHeader: false,
                      isEmbedded:
                          true, // Indiquer que le planning est utilisé en mode intégré
                      autoOpenTaskForm:
                          false, // Désactivation de l'ouverture automatique du formulaire
                      onClosed: () {
                        // Rafraîchir les tâches après la fermeture
                        widget.onTasksUpdated();
                      },
                      // Passer les variables d'état de la barre d'outils unifiée
                      todayButtonPressed: _todayButtonPressed,
                      timeScale: _currentTimeScale,
                      isCondensedMode: _isCondensedMode,
                      searchQuery: _searchQuery,
                      // Exposer la callback d'export PDF
                      onExportPdfCallback: (callback) {
                        _exportPdfCallback = callback;
                      },
                    );
                    return _planningScreen!;
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour exporter le planning en PDF (version corrigée - export direct)
  void _exportPlanningToPdf() {
    // Déclencher l'export PDF directement sans navigation
    _exportPlanningToPdfDirect();
  }

  // Méthode pour exporter le planning en PDF directement (nouvelle version)
  void _exportPlanningToPdfDirect() {
    // Déclencher l'export PDF directement depuis l'instance de EnhancedPlanningScreen
    if (_exportPdfCallback != null) {
      _exportPdfCallback!();
    } else {
      // Fallback si la callback n'est pas encore disponible
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Planning en cours de chargement, veuillez réessayer'),
        ),
      );
    }
  }

  // Méthode pour afficher le dialogue de gestion des versions
  void _navigateToBaselineManagement() {
    if (widget.projectData.planningId == null) return;

    // Afficher le dialogue modal de gestion des versions
    BaselineManagementDialog.show(
      context: context,
      planningId: widget.projectData.planningId!,
      planningName: widget.projectData.projectName ?? 'Planning du projet',
    );
  }

  /// Construit un bouton pour le sélecteur d'échelle temporelle
  Widget _buildTimeScaleButton(ThemeData theme, TimeScale scale, String label) {
    final isSelected = _currentTimeScale == scale;

    return InkWell(
      onTap: () {
        setState(() {
          _currentTimeScale = scale;
        });

        // Afficher un feedback à l'utilisateur
        String scaleName;
        switch (scale) {
          case TimeScale.week:
            scaleName = 'Semaine';
            break;
          case TimeScale.month:
            scaleName = 'Mois';
            break;
          case TimeScale.quarter:
            scaleName = 'Trimestre';
            break;
          case TimeScale.year:
            scaleName = 'Année';
            break;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Vue changée vers : $scaleName'),
            duration: const Duration(seconds: 1),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withAlpha(80)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontFamily: 'RedditSans',
            fontSize: 13,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface,
          ),
        ),
      ),
    );
  }
}
