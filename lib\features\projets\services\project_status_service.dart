import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/project_constants.dart';

/// Structure pour représenter un statut de projet
class ProjectStatusItem {
  final String dbValue;
  final String displayValue;
  final String iconKey; // Clé d'icône (ex: 'fiber_new')
  final int progressValue;

  const ProjectStatusItem({
    required this.dbValue,
    required this.displayValue,
    required this.iconKey,
    required this.progressValue,
  });

  Map<String, dynamic> toJson() => {
        'dbValue': dbValue,
        'displayValue': displayValue,
        'iconKey': iconKey,
        'progressValue': progressValue,
      };

  factory ProjectStatusItem.fromJson(Map<String, dynamic> json) {
    return ProjectStatusItem(
      dbValue: json['dbValue'],
      displayValue: json['displayValue'],
      iconKey: json['iconKey'] ?? 'folder',
      progressValue: json['progressValue'],
    );
  }
}

/// Service pour gérer les statuts de projet
///
/// Ce service permet de récupérer les statuts de projet depuis Firestore
/// et de les mettre en cache pour une utilisation ultérieure.
class ProjectStatusService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final Logger _logger = Logger('ProjectStatusService');

  // Clé pour le cache des statuts
  static const String _statusCacheKey = 'project_statuses';

  // Durée de validité du cache (24 heures)
  static const Duration _cacheDuration = Duration(hours: 24);

  // Clé pour stocker l'ID de l'organisation actuelle dans les préférences
  static const String _currentOrgKey = 'current_organization_id';

  /// Convertit les données JSON en IconData
  static IconData _getIconFromJson(Map<String, dynamic> json) {
    final codePoint = json['iconCodePoint'] as int;

    // Utiliser des constantes prédéfinies pour les icônes courantes
    if (codePoint == Icons.fiber_new.codePoint) return Icons.fiber_new;
    if (codePoint == Icons.draw.codePoint) return Icons.draw;
    if (codePoint == Icons.search.codePoint) return Icons.search;
    if (codePoint == Icons.architecture.codePoint) return Icons.architecture;
    if (codePoint == Icons.engineering.codePoint) return Icons.engineering;
    if (codePoint == Icons.construction.codePoint) return Icons.construction;
    if (codePoint == Icons.check_circle.codePoint) return Icons.check_circle;
    if (codePoint == Icons.pending.codePoint) return Icons.pending;
    if (codePoint == Icons.cancel.codePoint) return Icons.cancel;
    if (codePoint == Icons.pause.codePoint) return Icons.pause;
    if (codePoint == Icons.schedule.codePoint) return Icons.schedule;
    if (codePoint == Icons.assignment.codePoint) return Icons.assignment;
    if (codePoint == Icons.description.codePoint) return Icons.description;
    if (codePoint == Icons.folder.codePoint) return Icons.folder;
    if (codePoint == Icons.folder_open.codePoint) return Icons.folder_open;
    if (codePoint == Icons.folder_shared.codePoint) return Icons.folder_shared;
    if (codePoint == Icons.folder_special.codePoint) {
      return Icons.folder_special;
    }
    if (codePoint == Icons.folder_zip.codePoint) return Icons.folder_zip;
    if (codePoint == Icons.folder_off.codePoint) return Icons.folder_off;
    if (codePoint == Icons.folder_delete.codePoint) return Icons.folder_delete;
    if (codePoint == Icons.file_upload.codePoint) return Icons.file_upload;
    if (codePoint == Icons.file_download.codePoint) return Icons.file_download;
    if (codePoint == Icons.file_copy.codePoint) return Icons.file_copy;
    if (codePoint == Icons.drive_file_move.codePoint) {
      return Icons.drive_file_move;
    }

    // Si l'icône n'est pas prédéfinie, utiliser une icône par défaut
    return Icons.folder;
  }

  /// Obtenir l'ID de l'organisation actuelle depuis les préférences
  Future<String?> _getCurrentOrganizationId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_currentOrgKey);
    } catch (e) {
      _logger.warning(
          'Erreur lors de la récupération de l\'ID d\'organisation actuelle: $e');
      return null;
    }
  }

  /// Récupère les statuts de projet depuis Firestore ou le cache
  Future<List<ProjectStatusItem>> getProjectStatuses(
      {bool forceRefresh = false}) async {
    try {
      // Vérifier d'abord dans le cache si on ne force pas le rafraîchissement
      if (!forceRefresh) {
        final cachedStatuses = await _getCachedStatuses();
        if (cachedStatuses.isNotEmpty) {
          _logger.info(
              'Statuts de projet chargés depuis le cache (${cachedStatuses.length})');
          return cachedStatuses;
        }
      }

      // Récupérer l'ID de l'organisation actuelle
      final organizationId = await _getCurrentOrganizationId();

      // Construire la référence à la collection des statuts
      CollectionReference statusesRef;
      if (organizationId != null) {
        // Collection spécifique à l'organisation
        statusesRef = _db
            .collection('organizations')
            .doc(organizationId)
            .collection('project_statuses');
      } else {
        // Collection globale
        statusesRef = _db.collection('project_statuses');
      }

      // Récupérer les statuts depuis Firestore
      final snapshot = await statusesRef.orderBy('order').get();

      // Si aucun statut n'est défini, utiliser les statuts par défaut
      if (snapshot.docs.isEmpty) {
        _logger.info(
            'Aucun statut personnalisé trouvé, utilisation des statuts par défaut');
        final defaultStatuses = _getDefaultStatuses();

        // Mettre en cache les statuts par défaut
        await _cacheStatuses(defaultStatuses);

        return defaultStatuses;
      }

      // Convertir les documents en objets ProjectStatusItem
      final statuses = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;

        // Déterminer l'icône à utiliser
        IconData icon;
        if (data.containsKey('iconCodePoint')) {
          // Si l'icône est définie avec un code point
          icon = _getIconFromJson(data);
        } else {
          // Icône par défaut basée sur le statut
          icon = _getDefaultIconForStatus(data['dbValue']);
        }

        return ProjectStatusItem(
          dbValue: data['dbValue'] ?? doc.id,
          displayValue: data['displayValue'] ?? data['dbValue'] ?? doc.id,
          iconKey: _getIconKey(icon),
          progressValue: data['progressValue'] ?? 0,
        );
      }).toList();

      _logger.info(
          '${statuses.length} statuts de projet chargés depuis Firestore');

      // Mettre en cache les statuts
      await _cacheStatuses(statuses);

      return statuses;
    } catch (e) {
      _logger.warning('Erreur lors du chargement des statuts de projet: $e');

      // En cas d'erreur, utiliser les statuts par défaut
      return _getDefaultStatuses();
    }
  }

  /// Récupère les statuts depuis le cache
  Future<List<ProjectStatusItem>> _getCachedStatuses() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Vérifier si le cache existe et s'il est encore valide
      final cacheTimestamp = prefs.getInt('${_statusCacheKey}_timestamp');
      if (cacheTimestamp != null) {
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(cacheTimestamp);
        final now = DateTime.now();

        // Si le cache est périmé, retourner une liste vide
        if (now.difference(cacheTime) > _cacheDuration) {
          _logger.info('Cache des statuts périmé');
          return [];
        }

        // Récupérer les statuts du cache
        final statusesJson = prefs.getStringList(_statusCacheKey);
        if (statusesJson != null && statusesJson.isNotEmpty) {
          // Convertir les JSON en objets ProjectStatusItem
          final statuses = statusesJson.map((json) {
            return ProjectStatusItem.fromJson(
              Map<String, dynamic>.from(
                Map<String, dynamic>.from(
                  // ignore: unnecessary_cast
                  (jsonDecode(json) as Map<String, dynamic>),
                ),
              ),
            );
          }).toList();

          return statuses;
        }
      }

      return [];
    } catch (e) {
      _logger
          .warning('Erreur lors de la récupération du cache des statuts: $e');
      return [];
    }
  }

  /// Met en cache les statuts
  Future<void> _cacheStatuses(List<ProjectStatusItem> statuses) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convertir les statuts en JSON
      final statusesJson = statuses.map((status) {
        return jsonEncode(status.toJson());
      }).toList();

      // Enregistrer les statuts et l'horodatage
      await prefs.setStringList(_statusCacheKey, statusesJson);
      await prefs.setInt(
        '${_statusCacheKey}_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.info('${statuses.length} statuts mis en cache');
    } catch (e) {
      _logger.warning('Erreur lors de la mise en cache des statuts: $e');
    }
  }

  /// Retourne les statuts par défaut
  List<ProjectStatusItem> _getDefaultStatuses() {
    return [
      const ProjectStatusItem(
        dbValue: ProjectStatus.nouveau,
        displayValue: ProjectStatus.nouveauDisplay,
        iconKey: 'fiber_new',
        progressValue: 10,
      ),
      const ProjectStatusItem(
        dbValue: 'esquisse',
        displayValue: 'Esquisse',
        iconKey: 'draw',
        progressValue: 15,
      ),
      const ProjectStatusItem(
        dbValue: 'etude_audit',
        displayValue: 'Étude / Audit',
        iconKey: 'search',
        progressValue: 20,
      ),
      const ProjectStatusItem(
        dbValue: 'aps',
        displayValue: 'Avant-Projet Sommaire',
        iconKey: 'description_outlined',
        progressValue: 25,
      ),
      const ProjectStatusItem(
        dbValue: 'conception',
        displayValue: 'Conception',
        iconKey: 'architecture',
        progressValue: 30,
      ),
      const ProjectStatusItem(
        dbValue: 'apd',
        displayValue: 'Avant-Projet Définitif',
        iconKey: 'article_outlined',
        progressValue: 35,
      ),
      const ProjectStatusItem(
        dbValue: 'dossiers_admin',
        displayValue: 'Dossiers Admin',
        iconKey: 'folder_special',
        progressValue: 40,
      ),
      const ProjectStatusItem(
        dbValue: 'dce',
        displayValue: 'Dossier de Consultation',
        iconKey: 'folder_copy_outlined',
        progressValue: 45,
      ),
      const ProjectStatusItem(
        dbValue: 'analyse_offres',
        displayValue: 'Analyse des Offres',
        iconKey: 'analytics_outlined',
        progressValue: 55,
      ),
      const ProjectStatusItem(
        dbValue: ProjectStatus.enCours,
        displayValue: ProjectStatus.enCoursDisplay,
        iconKey: 'engineering',
        progressValue: 60,
      ),
      const ProjectStatusItem(
        dbValue: 'suivi_chantier',
        displayValue: 'Suivi de Chantier',
        iconKey: 'build_outlined',
        progressValue: 70,
      ),
      const ProjectStatusItem(
        dbValue: 'execution',
        displayValue: 'Exécution',
        iconKey: 'construction',
        progressValue: 80,
      ),
      const ProjectStatusItem(
        dbValue: 'reception',
        displayValue: 'Réception',
        iconKey: 'check_circle_outline',
        progressValue: 90,
      ),
      const ProjectStatusItem(
        dbValue: ProjectStatus.termine,
        displayValue: ProjectStatus.termineDisplay,
        iconKey: 'task_alt',
        progressValue: 100,
      ),
    ];
  }

  /// Retourne l'icône par défaut pour un statut donné
  IconData _getDefaultIconForStatus(String? status) {
    switch (status) {
      case ProjectStatus.nouveau:
        return Icons.fiber_new;
      case 'esquisse':
        return Icons.draw;
      case 'etude_audit':
        return Icons.search;
      case 'projet':
        return Icons.architecture;
      case 'execution':
        return Icons.engineering;
      case 'chantier':
        return Icons.construction;
      case 'termine':
        return Icons.check_circle;
      case 'en_attente':
        return Icons.pending;
      case 'annule':
        return Icons.cancel;
      case 'en_pause':
        return Icons.pause;
      case 'planifie':
        return Icons.schedule;
      case 'en_cours':
        return Icons.assignment;
      default:
        return Icons.folder;
    }
  }

  /// Retourne la clé d'icône pour un IconData donné
  String _getIconKey(IconData icon) {
    // Utiliser des constantes prédéfinies pour les icônes courantes
    if (icon == Icons.fiber_new) return 'fiber_new';
    if (icon == Icons.draw) return 'draw';
    if (icon == Icons.search) return 'search';
    if (icon == Icons.architecture) return 'architecture';
    if (icon == Icons.engineering) return 'engineering';
    if (icon == Icons.construction) return 'construction';
    if (icon == Icons.check_circle) return 'check_circle';
    if (icon == Icons.pending) return 'pending';
    if (icon == Icons.cancel) return 'cancel';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.assignment) return 'assignment';
    if (icon == Icons.description) return 'description';
    if (icon == Icons.folder) return 'folder';
    if (icon == Icons.folder_open) return 'folder_open';
    if (icon == Icons.folder_shared) return 'folder_shared';
    if (icon == Icons.folder_special) return 'folder_special';
    if (icon == Icons.folder_zip) return 'folder_zip';
    if (icon == Icons.folder_off) return 'folder_off';
    if (icon == Icons.folder_delete) return 'folder_delete';
    if (icon == Icons.file_upload) return 'file_upload';
    if (icon == Icons.file_download) return 'file_download';
    if (icon == Icons.file_copy) return 'file_copy';
    if (icon == Icons.drive_file_move) return 'drive_file_move';

    // Si l'icône n'est pas prédéfinie, créer une nouvelle instance
    return '${icon.codePoint}';
  }
}
