import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/features/projets/constants/project_constants.dart';
import 'package:seqqo/features/projets/models/project_data.dart';
import 'package:seqqo/features/clients/models/client_data.dart';
import 'package:seqqo/features/projets/services/project_storage_service.dart';
import 'package:seqqo/features/clients/services/client_storage_service.dart';
import 'package:seqqo/features/planning/services/planning_storage_service.dart';
import 'package:seqqo/features/planning/models/planning_data.dart'; // Added for PlanningData
import 'package:seqqo/core/utils/feedback_utils.dart';
import 'package:seqqo/features/clients/utils/enseigne_utils.dart';

class ProjectEditDialog extends StatefulWidget {
  final ProjectData? initialProject;
  final Function? onProjectSaved;

  const ProjectEditDialog({
    super.key,
    this.initialProject,
    this.onProjectSaved,
  });

  @override
  State<ProjectEditDialog> createState() => _ProjectEditDialogState();
}

class _ProjectEditDialogState extends State<ProjectEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _uuid = const Uuid();
  final _projectService = ProjectStorageService();
  final _clientService = ClientStorageService();
  late PlanningStorageService _planningService;
  late ProjectData _currentProject;
  bool _isNewProject = true;
  bool _isSaving = false;
  bool _clientsLoading = true;
  List<ClientData> _availableClients = [];
  Timer? _autoSaveTimer;
  bool _hasUnsavedChanges = false;

  // Contrôleurs pour les champs de texte
  final _projectNameController = TextEditingController();
  final _projectNumberController = TextEditingController();
  final _siteAddressController = TextEditingController();
  final _siteCityController = TextEditingController();
  final _sitePostalCodeController = TextEditingController();
  final _notesController = TextEditingController();

  // État pour les dropdowns
  String? _selectedClientId;
  String _selectedStatus = 'nouveau';
  String _selectedEnseigneType = '';

  // Options pour les dropdowns
  // Utiliser les constantes définies dans ProjectStatus
  final List<String> _statusOptions = ProjectStatus.values;

  // Utilisation de la liste des types d'enseigne définie dans la fonctionnalité clients
  final List<String> _enseigneTypes = enseigneTypes;

  @override
  void initState() {
    super.initState();
    _initializeProject(); // Initializes _currentProject
    // Initialize _planningService after _currentProject is initialized
    _planningService = PlanningStorageService(
        firestore: FirebaseFirestore.instance,
        planningId: _currentProject.planningId ?? '');
    _loadClientsForDropdown();

    // Configuration de l'auto-save
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_hasUnsavedChanges && mounted) {
        _saveProject(showFeedback: false);
      }
    });
  }

  @override
  void dispose() {
    // Annuler les timers
    _autoSaveTimer?.cancel();

    // Disposer les contrôleurs
    _projectNameController.dispose();
    _projectNumberController.dispose();
    _siteAddressController.dispose();
    _siteCityController.dispose();
    _sitePostalCodeController.dispose();
    _notesController.dispose();

    super.dispose();
  }

  void _initializeProject() {
    if (widget.initialProject != null) {
      _currentProject = widget.initialProject!;
      _isNewProject = false;
      _selectedClientId = _currentProject.clientId;
      _selectedStatus = _currentProject.status;
      _selectedEnseigneType = _currentProject.enseigneType ?? '';
    } else {
      // Créer un nouveau projet avec un ID unique
      _currentProject = ProjectData(
        projectId: _uuid.v4(),
        clientId:
            '', // Champ requis, sera mis à jour lors de la sélection du client
        createdAt: Timestamp.now(),
        status: 'nouveau',
      );
      _isNewProject = true;
    }

    // Initialiser les contrôleurs avec les valeurs actuelles
    _projectNameController.text = _currentProject.projectName ?? '';
    _projectNumberController.text = _currentProject.projectNumber ?? '';
    _siteAddressController.text = _currentProject.siteAddress ?? '';
    _siteCityController.text = _currentProject.siteCity ?? '';
    _sitePostalCodeController.text = _currentProject.sitePostalCode ?? '';
    _notesController.text = _currentProject.notes ?? '';

    // Ajouter les listeners pour détecter les changements
    _projectNameController.addListener(_onFieldChanged);
    _projectNumberController.addListener(_onFieldChanged);
    _siteAddressController.addListener(_onFieldChanged);
    _siteCityController.addListener(_onFieldChanged);
    _sitePostalCodeController.addListener(_onFieldChanged);
    _notesController.addListener(_onFieldChanged);
  }

  Future<void> _loadClientsForDropdown() async {
    if (!mounted) return;
    setState(() {
      _clientsLoading = true;
    });
    try {
      final clients = await _clientService.listClients();
      if (mounted) {
        setState(() {
          _availableClients = clients;
          _clientsLoading = false;
          if (_selectedClientId != null &&
              !_availableClients.any((c) => c.clientId == _selectedClientId)) {
            _selectedClientId = null;
            _currentProject = _currentProject.copyWith(clientId: '');
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _clientsLoading = false;
        });
        showFeedbackSnackBar(
          context,
          message: "Erreur chargement clients",
          isError: true,
        );
      }
    }
  }

  void _onFieldChanged() {
    _hasUnsavedChanges = true;
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 2), () {
      if (_hasUnsavedChanges && mounted) {
        _saveProject(showFeedback: false);
      }
    });
  }

  Future<void> _saveProject({bool showFeedback = true}) async {
    if (!(_formKey.currentState?.validate() ?? false)) {
      if (showFeedback) {
        showFeedbackSnackBar(
          context,
          message: 'Veuillez corriger les erreurs.',
          isError: true,
        );
      }
      return;
    }
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    // Mettre à jour l'objet projet avec les valeurs des contrôleurs
    _currentProject = _currentProject.copyWith(
      projectName: _projectNameController.text.trim(),
      projectNumber: _projectNumberController.text.trim(),
      siteAddress: _siteAddressController.text.trim(),
      siteCity: _siteCityController.text.trim(),
      sitePostalCode: _sitePostalCodeController.text.trim(),
      notes: _notesController.text.trim(),
      clientId: _selectedClientId ?? '',
      status: _selectedStatus,
      enseigneType: _selectedEnseigneType,
      // Note: ProjectData n'a pas de champ updatedAt, nous utilisons createdAt pour les nouveaux projets
    );

    bool success = false;
    try {
      // Si c'est un nouveau projet, créer un planning automatiquement
      if (_isNewProject) {
        // Sauvegarder d'abord le projet pour obtenir un ID valide
        success = await _projectService.saveProject(_currentProject);

        if (success) {
          // Créer un planning pour le projet
          final newPlanningId = _uuid.v4();
          final newPlanningData = PlanningData(
            planningId: newPlanningId,
            projectId: _currentProject.projectId,
            name:
                'Planning for ${_currentProject.projectName ?? _currentProject.projectNumber}',
          );
          try {
            await FirebaseFirestore.instance
                .collection('plannings')
                .doc(newPlanningId)
                .set(newPlanningData.toJson());

            // Mettre à jour le projet avec le planningId
            _currentProject =
                _currentProject.copyWith(planningId: newPlanningId);
            success = await _projectService.saveProject(_currentProject);
          } catch (e) {
            // Handle error during planning creation
            success = false;
            if (mounted) {
              showFeedbackSnackBar(
                context,
                message: 'Erreur création planning: $e',
                isError: true,
              );
            }
          }
        }
      } else {
        // Projet existant, mise à jour normale
        success = await _projectService.saveProject(_currentProject);
      }

      _hasUnsavedChanges = !success;
    } catch (e) {
      success = false;
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
        if (showFeedback) {
          showFeedbackSnackBar(
            context,
            message:
                success ? 'Projet sauvegardé !' : 'Erreur sauvegarde projet.',
            isError: !success,
          );
          if (success) {
            if (widget.onProjectSaved != null) {
              widget.onProjectSaved!();
            }
            Navigator.pop(context); // Ferme le dialogue
          }
        }
      }
    }
  }

  Widget _buildClientDropdown(ThemeData theme) {
    return DropdownButtonFormField<String>(
      value: _selectedClientId,
      hint:
          const Text('Sélectionner un client', style: TextStyle(fontSize: 14)),
      isExpanded: true,
      style: const TextStyle(fontSize: 14),
      decoration: InputDecoration(
        labelText: 'Client *',
        labelStyle: const TextStyle(fontSize: 14),
        prefixIcon: const Icon(Icons.business_outlined, size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerLowest,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        suffixIcon: _clientsLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              )
            : null,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Veuillez sélectionner un client';
        }
        return null;
      },
      items: _availableClients.map((ClientData client) {
        return DropdownMenuItem<String>(
          value: client.clientId,
          child: Text(
            client.name ?? 'Client sans nom',
            style: const TextStyle(fontSize: 14),
          ),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedClientId = newValue;
          _currentProject = _currentProject.copyWith(clientId: newValue ?? '');

          // Mise à jour automatique du type d'enseigne en fonction du client sélectionné
          if (newValue != null && newValue.isNotEmpty) {
            // Trouver le client sélectionné
            final selectedClient = _availableClients.firstWhere(
              (client) => client.clientId == newValue,
              orElse: () => ClientData(clientId: ''),
            );

            // Si le client a un type d'enseigne défini, l'utiliser
            if (selectedClient.enseigneType != null &&
                selectedClient.enseigneType!.isNotEmpty &&
                _enseigneTypes.contains(selectedClient.enseigneType)) {
              _selectedEnseigneType = selectedClient.enseigneType!;

              // Afficher un message d'information
              if (mounted && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Type d\'enseigne "${selectedClient.enseigneType}" automatiquement sélectionné d\'après le client',
                      style: const TextStyle(fontSize: 14),
                    ),
                    duration: const Duration(seconds: 3),
                    behavior: SnackBarBehavior.floating,
                    width: 400,
                    backgroundColor:
                        Theme.of(context).colorScheme.primaryContainer,
                    action: SnackBarAction(
                      label: 'OK',
                      textColor:
                          Theme.of(context).colorScheme.onPrimaryContainer,
                      onPressed: () {},
                    ),
                  ),
                );
              }
            }
          }
        });
        _onFieldChanged();
      },
    );
  }

  Widget _buildStatusDropdown(ThemeData theme) {
    return DropdownButtonFormField<String>(
      value: _selectedStatus,
      isExpanded: true,
      style: const TextStyle(fontSize: 14),
      decoration: InputDecoration(
        labelText: 'Statut',
        labelStyle: const TextStyle(fontSize: 14),
        prefixIcon: const Icon(Icons.flag_outlined, size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerLowest,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      ),
      items: _statusOptions.map((String status) {
        String displayText;
        IconData icon;
        Color color;

        switch (status) {
          case ProjectStatus.nouveau:
            displayText = ProjectStatus.nouveauDisplay;
            icon = Icons.fiber_new_outlined;
            color = Colors.orange;
            break;
          case ProjectStatus.enCours:
            displayText = ProjectStatus.enCoursDisplay;
            icon = Icons.pending_outlined;
            color = Colors.green;
            break;
          case ProjectStatus.termine:
            displayText = ProjectStatus.termineDisplay;
            icon = Icons.check_circle_outline;
            color = Colors.blue;
            break;
          default:
            displayText = status;
            icon = Icons.help_outline;
            color = Colors.grey;
        }

        return DropdownMenuItem<String>(
          value: status,
          child: Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                displayText,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (String? newValue) {
        if (newValue != null) {
          setState(() {
            _selectedStatus = newValue;
          });
          _onFieldChanged();
        }
      },
    );
  }

  Widget _buildEnseigneTypeDropdown(ThemeData theme) {
    // Assure que la valeur sélectionnée est valide ou null
    String? validSelectedType = _selectedEnseigneType.isNotEmpty &&
            _enseigneTypes.contains(_selectedEnseigneType)
        ? _selectedEnseigneType
        : null;

    return DropdownButtonFormField<String>(
      value: validSelectedType,
      hint: const Text('Sélectionner un type', style: TextStyle(fontSize: 14)),
      isExpanded: true,
      style: const TextStyle(fontSize: 14),
      decoration: InputDecoration(
        labelText: 'Type d\'enseigne',
        labelStyle: const TextStyle(fontSize: 14),
        prefixIcon: validSelectedType == null
            ? const Icon(Icons.category_outlined, size: 20)
            : Icon(
                getIconForEnseigneType(validSelectedType),
                size: 20,
                color: getColorForEnseigneType(validSelectedType),
              ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerLowest,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      ),
      items: _enseigneTypes.map((String type) {
        return DropdownMenuItem<String>(
          value: type,
          child: Row(
            children: [
              Icon(
                getIconForEnseigneType(type),
                size: 20,
                color: getColorForEnseigneType(type),
              ),
              const SizedBox(width: 8),
              Text(
                type,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedEnseigneType = newValue ?? '';
        });
        _onFieldChanged();
      },
      // Personnaliser l'affichage de l'élément sélectionné dans le bouton
      selectedItemBuilder: (BuildContext context) {
        return _enseigneTypes.map<Widget>((String item) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                getIconForEnseigneType(item),
                size: 16,
                color: getColorForEnseigneType(item),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  item,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        }).toList();
      },
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    IconData? icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
  }) {
    final theme = Theme.of(context);
    return TextFormField(
      controller: controller,
      style: const TextStyle(fontSize: 14),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(fontSize: 14),
        prefixIcon: icon != null ? Icon(icon, size: 20) : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide:
              BorderSide(color: theme.colorScheme.outline.withOpacity(0.5)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide:
              BorderSide(color: theme.colorScheme.outline.withOpacity(0.5)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide:
              BorderSide(color: theme.colorScheme.primary.withOpacity(0.7)),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerLowest,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        hintStyle: TextStyle(
          fontSize: 14,
          color: theme.colorScheme.onSurface.withOpacity(0.5),
        ),
      ),
      validator: validator,
      maxLines: maxLines,
      keyboardType: keyboardType,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      backgroundColor: theme.colorScheme.surface,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 16 : size.width * 0.15,
        vertical: isSmallScreen ? 24 : size.height * 0.1,
      ),
      child: Container(
        width: isSmallScreen ? double.infinity : 800,
        constraints: BoxConstraints(
          maxWidth: 800,
          maxHeight: isSmallScreen ? size.height * 0.8 : 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête du dialogue
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.folder_outlined,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isNewProject ? 'Nouveau Projet' : 'Modifier Projet',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (_isSaving)
                    Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.only(right: 8),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Fermer',
                  ),
                ],
              ),
            ),

            // Corps du formulaire avec défilement
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Client Dropdown
                      _buildClientDropdown(theme),
                      const SizedBox(height: 16),

                      // Nom du projet
                      _buildTextField(
                        controller: _projectNameController,
                        label: 'Nom du Projet *',
                        icon: Icons.folder_copy_outlined,
                        validator: (value) =>
                            (value == null || value.trim().isEmpty)
                                ? 'Le nom du projet est obligatoire'
                                : null,
                      ),
                      const SizedBox(height: 16),

                      // Numéro de projet
                      _buildTextField(
                        controller: _projectNumberController,
                        label: 'Numéro de Projet/Affaire',
                        icon: Icons.tag,
                      ),
                      const SizedBox(height: 16),

                      // Adresse du site
                      _buildTextField(
                        controller: _siteAddressController,
                        label: 'Adresse du Site',
                        icon: Icons.location_on_outlined,
                      ),
                      const SizedBox(height: 16),

                      // Ville et code postal
                      Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: _buildTextField(
                              controller: _siteCityController,
                              label: 'Ville',
                              icon: Icons.location_city_outlined,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            flex: 1,
                            child: _buildTextField(
                              controller: _sitePostalCodeController,
                              label: 'Code Postal',
                              icon: Icons.pin_outlined,
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Statut et type d'enseigne
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatusDropdown(theme),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildEnseigneTypeDropdown(theme),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Notes
                      _buildTextField(
                        controller: _notesController,
                        label: 'Notes Générales',
                        icon: Icons.notes,
                        maxLines: 4,
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),

            // Pied de page avec boutons d'action
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerLow,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isSaving ? null : () => _saveProject(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      textStyle: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    child: Text(_isNewProject ? 'Créer' : 'Enregistrer'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
